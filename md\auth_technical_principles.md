# 认证技术原理对比

## OAuth 2.0 技术原理
1. **核心概念**
   - 资源所有者（Resource Owner）
   - 客户端（Client）
   - 授权服务器（Authorization Server）
   - 资源服务器（Resource Server）

2. **工作流程**
   ```mermaid
   sequenceDiagram
       participant U as 用户
       participant C as 客户端
       participant A as 授权服务器
       participant R as 资源服务器
       
       C->>U: 请求授权
       U->>A: 同意授权
       C->>A: 请求访问令牌
       A->>C: 返回访问令牌
       C->>R: 使用令牌访问资源
       R->>C: 返回受保护资源
   ```

3. **授权模式**
   - 授权码模式（Authorization Code）
   - 隐式授权（Implicit）
   - 密码模式（Password）
   - 客户端模式（Client Credentials）

## JWT 技术原理
1. **令牌结构**
   ```
   Header.Payload.Signature
   ```
   - Header（头部）：算法与令牌类型
   - Payload（负载）：声明信息
   - Signature（签名）：验证信息

2. **工作流程**
   ```mermaid
   sequenceDiagram
       participant C as 客户端
       participant S as 服务器
       
       C->>S: 发送认证信息
       S->>S: 生成JWT
       S->>C: 返回JWT
       C->>S: 请求资源(带JWT)
       S->>S: 验证JWT
       S->>C: 返回资源
   ```

3. **验证机制**
   - 基于签名验证
   - 无状态验证
   - 自包含信息验证

## SAML 2.0 技术原理
1. **核心组件**
   - 身份提供者（IdP）
   - 服务提供者（SP）
   - XML安全断言

2. **工作流程**
   ```mermaid
   sequenceDiagram
       participant U as 用户
       participant SP as 服务提供者
       participant IdP as 身份提供者
       
       U->>SP: 访问服务
       SP->>IdP: SAML认证请求
       IdP->>U: 认证用户
       U->>IdP: 提供凭证
       IdP->>SP: SAML断言
       SP->>U: 访问授权
   ```

3. **断言类型**
   - 认证断言
   - 属性断言
   - 授权决策断言

## OpenID Connect 技术原理
1. **协议层次**
   - 身份层（ID Token）
   - OAuth 2.0授权层
   - REST/JSON消息层

2. **工作流程**
   ```mermaid
   sequenceDiagram
       participant U as 用户
       participant C as 客户端
       participant P as OpenID提供者
       
       C->>P: 认证请求
       P->>U: 用户认证
       P->>C: ID Token + Access Token
       C->>P: 获取用户信息
       P->>C: 用户信息
   ```

3. **令牌类型**
   - ID Token（身份令牌）
   - Access Token（访问令牌）
   - Refresh Token（刷新令牌）

## LDAP 技术原理
1. **目录结构**
   ```
   dc=组织,dc=com
   ├── ou=部门
   │   ├── cn=用户1
   │   └── cn=用户2
   └── ou=组
       ├── cn=组1
       └── cn=组2
   ```

2. **工作流程**
   ```mermaid
   sequenceDiagram
       participant C as 客户端
       participant L as LDAP服务器
       
       C->>L: 绑定请求(DN+密码)
       L->>C: 绑定响应
       C->>L: 搜索/查询请求
       L->>C: 返回结果
   ```

3. **操作类型**
   - 绑定（Bind）
   - 搜索（Search）
   - 比较（Compare）
   - 修改（Modify）

## 技术原理对比表

| 特性 | OAuth 2.0 | JWT | SAML 2.0 | OpenID Connect | LDAP |
|------|-----------|-----|----------|----------------|------|
| 认证方式 | 授权码/令牌 | 签名令牌 | XML断言 | ID Token | 绑定认证 |
| 信息传递 | 令牌传递 | 自包含令牌 | XML文档 | JSON令牌 | LDAP协议 |
| 会话管理 | 服务端管理 | 无状态 | 服务端管理 | 服务端管理 | 绑定会话 |
| 密钥管理 | 客户端密钥 | 签名密钥 | 证书密钥 | 多重密钥 | 简单认证 |
| 协议复杂度 | 中等 | 简单 | 复杂 | 中等 | 中等 |

## 安全机制对比

1. **OAuth 2.0**
   - HTTPS传输
   - 令牌有效期控制
   - 状态参数防护
   - 重定向URI验证

2. **JWT**
   - 签名验证
   - 过期时间检查
   - 算法选择
   - 载荷加密

3. **SAML 2.0**
   - XML签名
   - XML加密
   - 证书验证
   - 时间戳验证

4. **OpenID Connect**
   - 混合流程安全
   - 令牌绑定
   - 声明加密
   - 会话管理

5. **LDAP**
   - 简单认证
   - SASL机制
   - TLS加密
   - 访问控制列表 