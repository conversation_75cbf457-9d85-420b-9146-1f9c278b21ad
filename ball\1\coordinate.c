#include <windows.h>
#include "resource.h"
#include <stdio.h>

// 全局变量
HINSTANCE hInst;
double x, y; // 计算结果
BOOL hasResult = FALSE; // 是否有计算结果

// 函数声明
BOOL CALLBACK DialogProc(HWND, UINT, WPARAM, LPARAM);
void CalculateCenter(HWND hwnd);

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, 
    LPSTR lpCmdLine, int nCmdShow)
{
    hInst = hInstance;
    DialogBox(hInstance, MAKEINTRESOURCE(IDD_MAIN_DIALOG), NULL, DialogProc);
    return 0;
}

BOOL CALLBACK DialogProc(HWND hwnd, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message)
    {
        case WM_INITDIALOG:
            return TRUE;

        case WM_COMMAND:
            switch (LOWORD(wParam))
            {
                case IDC_CALC_BUTTON:
                    CalculateCenter(hwnd);
                    return TRUE;

                case IDCANCEL:
                    EndDialog(hwnd, 0);
                    return TRUE;
            }
            break;

        case WM_PAINT:
            if (hasResult)
            {
                PAINTSTRUCT ps;
                HDC hdc = BeginPaint(hwnd, &ps);
                char result[100];
                sprintf(result, "圆心坐标: (%.4f, %.4f)", x, y);
                TextOut(hdc, 20, 200, result, strlen(result));
                EndPaint(hwnd, &ps);
            }
            return TRUE;
    }
    return FALSE;
}

void CalculateCenter(HWND hwnd)
{
    char buffer[32];
    double a, b, c, d, e, f;
    double aa, bb, cc, dd, ee, ff;

    // 获取输入值
    GetDlgItemText(hwnd, IDC_EDIT1, buffer, 32);
    a = atof(buffer);
    GetDlgItemText(hwnd, IDC_EDIT2, buffer, 32);
    b = atof(buffer);
    GetDlgItemText(hwnd, IDC_EDIT3, buffer, 32);
    c = atof(buffer);
    GetDlgItemText(hwnd, IDC_EDIT4, buffer, 32);
    d = atof(buffer);
    GetDlgItemText(hwnd, IDC_EDIT5, buffer, 32);
    e = atof(buffer);
    GetDlgItemText(hwnd, IDC_EDIT6, buffer, 32);
    f = atof(buffer);

    // 计算
    aa = a - c;
    bb = b - d;
    cc = a - e;
    dd = b - f;
    ee = (a * a - c * c + b * b - d * d) / 2;
    ff = (a * a - e * e + b * b - f * f) / 2;

    if ((bb * cc - aa * dd) == 0) {
        MessageBox(hwnd, "计算过程中出现除零错误！", "错误", MB_ICONERROR);
        return;
    }

    x = (bb * ff - dd * ee) / (bb * cc - aa * dd);
    y = (cc * ee - aa * ff) / (bb * cc - aa * dd);
    
    hasResult = TRUE;
    InvalidateRect(hwnd, NULL, TRUE);
}