import math

class ProjectilePhysics:
    def __init__(self):
        self.gravity = 0.05
        self.power = 100  # 固定满力
        
    def calculate_angle(self, grid_count):
        """根据方格数计算满力情况下所需的角度"""
        # 已知实际测试数据（反过来）：
        # 24格需要71度
        # 18格需要73度
        # 15格需要75度
        
        if grid_count >= 24:
            return 71
        elif grid_count >= 18:
            # 18-24格之间，角度从73-71度线性变化
            extra_grids = grid_count - 18
            extra_angle = -(extra_grids / 6) * 2  # 每6格减少2度
            angle = 73 + extra_angle
        elif grid_count >= 15:
            # 15-18格之间，角度从75-73度线性变化
            extra_grids = grid_count - 15
            extra_angle = -(extra_grids / 3) * 2  # 每3格减少2度
            angle = 75 + extra_angle
        else:
            return 75  # 15格以下用75度
                
        return round(angle, 1)

    def calculate_hit_point(self, start_x, start_y, angle, power):
        """计算炮弹落点"""
        angle_rad = math.radians(angle)
        velocity = power
        
        velocity_x = velocity * math.cos(angle_rad)
        velocity_y = velocity * math.sin(angle_rad)
        
        # 计算落点
        # x = x0 + vx * t
        # y = y0 + vy * t - 0.5 * g * t^2
        
        # 计算飞行时间 (解二次方程)
        a = -0.5 * self.gravity
        b = velocity_y
        c = start_y - 500  # 目标高度是500
        
        discriminant = b*b - 4*a*c
        if discriminant < 0:
            return None
            
        t = (-b + math.sqrt(discriminant)) / (2*a)
        if t <= 0:
            return None
            
        x = start_x + velocity_x * t
        y = 500
        
        return (x, y)

    def get_shot_suggestion(self, start_x, start_y, target_x, target_y):
        """获取射击建议"""
        angle = self.calculate_angle(target_x - start_x)
        return f"建议: 角度={angle}° 力量=100%"

# 使用示例
if __name__ == "__main__":
    physics = ProjectilePhysics()
    
    # 数一下地面方格数
    grid_count = int(input("输入两个角色之间的方格数: "))
    
    angle = physics.calculate_angle(grid_count)
    print(f"方格数: {grid_count}")
    print(f"推荐角度: {angle}°")
    print(f"推荐力量: 100%")
    
    hit_point = physics.calculate_hit_point(200, 500, angle, physics.power)
    print(f"预计落点: X={hit_point[0]:.1f}")
    print(f"误差: {abs(hit_point[0] - (200 + grid_count * 10)):.1f}像素") 