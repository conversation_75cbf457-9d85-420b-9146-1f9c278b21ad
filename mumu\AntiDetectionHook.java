import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedHelpers;
import java.io.File;

public class AntiDetectionHook {

    public static void hook(LoadPackageParam lpparam) {
        // 修改IMEI检测
        XposedHelpers.findAndHookMethod("android.telephony.TelephonyManager", 
            lpparam.classLoader, "getDeviceId", new XC_MethodHook() {
            @Override
            protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                param.setResult("真实设备IMEI");
            }
        });

        // 修改文件检测
        XposedHelpers.findAndHookMethod("java.io.File", 
            lpparam.classLoader, "exists", new XC_MethodHook() {
            @Override
            protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                File file = (File) param.thisObject;
                String path = file.getPath();
                if (path.contains("/system/app/Superuser.apk") || 
                    path.contains("/sbin/su") ||
                    path.contains("/system/bin/failsafe/su")) {
                    param.setResult(false);
                }
            }
        });
    }
} 