import tkinter as tk
from tkinter import ttk, messagebox
import math
import re

class CircumcenterCalculator:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title('三角形外心计算器')
        
        # 设置窗口大小和位置
        window_width = 400
        window_height = 450
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.window.geometry(f'{window_width}x{window_height}+{x}+{y}')
        
        # 设置窗口样式
        self.window.configure(bg='#f0f0f0')
        self.style = ttk.Style()
        self.style.configure('Title.TLabel', font=('微软雅黑', 16, 'bold'), background='#f0f0f0')
        self.style.configure('Input.TLabel', font=('微软雅黑', 10), background='#f0f0f0')
        self.style.configure('Result.TLabel', font=('微软雅黑', 12), background='#f0f0f0')
        self.style.configure('Author.TLabel', font=('微软雅黑', 9), background='#f0f0f0')
        
        # 记录第一个有效输入的位数
        self.digit_limit = None
        
        # 创建主框架
        main_frame = ttk.Frame(self.window, padding="20 10")
        main_frame.pack(fill='both', expand=True)
        
        # 创建输入模式切换开关
        self.input_mode_var = tk.BooleanVar(value=False)
        mode_frame = ttk.Frame(main_frame)
        mode_frame.pack(fill='x', pady=(0, 10))
        mode_switch = ttk.Checkbutton(
            mode_frame,
            text='使用(x,y)格式输入',
            variable=self.input_mode_var,
            command=self.toggle_input_mode,
            style='Input.TCheckbutton'
        )
        mode_switch.pack(side='right')
        
        # 配置Checkbutton样式
        self.style.configure('Input.TCheckbutton', font=('微软雅黑', 10), background='#f0f0f0')
        
        # 创建标题
        title = ttk.Label(
            main_frame, 
            text='三角形外心计算器',
            style='Title.TLabel',
            anchor='center'
        )
        title.pack(pady=(0, 20))
        
        # 创建输入框和标签
        self.create_input_fields(main_frame)
        
        # 创建按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=20)
        
        # 自定义按钮样式
        self.style.configure('Action.TButton', font=('微软雅黑', 10))
        
        # 创建计算按钮
        self.calc_button = ttk.Button(
            button_frame,
            text='计算外心',
            command=self.calculate_circumcenter,
            style='Action.TButton',
            width=15
        )
        self.calc_button.pack(side='left', padx=10)
        
        # 创建重置按钮
        self.reset_button = ttk.Button(
            button_frame,
            text='重置',
            command=self.reset_inputs,
            style='Action.TButton',
            width=15
        )
        self.reset_button.pack(side='left', padx=10)
        
        # 创建结果框架
        result_frame = ttk.LabelFrame(main_frame, text='计算结果', padding="10")
        result_frame.pack(fill='x', pady=(0, 20))
        
        # 创建结果标签
        self.result_label = ttk.Label(
            result_frame,
            text='外心坐标将在这里显示',
            style='Result.TLabel',
            anchor='center'
        )
        self.result_label.pack(pady=10)
        
        # 添加分隔线
        ttk.Separator(main_frame, orient='horizontal').pack(fill='x', pady=10)
        
        # 添加作者信息
        author_label = ttk.Label(
            main_frame,
            text='作者：凉巢萎',
            style='Author.TLabel',
            anchor='e'
        )
        author_label.pack(side='bottom', fill='x', pady=5)
        
    def on_focus_out(self, event):
        # 当输入框失去焦点时触发
        if self.digit_limit is None:
            # 获取当前输入框的值
            value = event.widget.get().replace('-', '').strip()
            if value:
                # 设置位数限制
                self.digit_limit = len(value)
    
    def validate_number(self, P, W):
        # 如果是删除操作，允许
        if P == "":
            return True
        
        if self.input_mode_var.get():
            # (x,y)格式验证
            if not re.match(r'^\(?(\-?\d*,?\-?\d*)?\)?$', P):
                return False
        else:
            # 单独数字格式验证
            if not re.match(r'^-?\d*$', P):
                return False
            
            # 获取数字部分
            num_part = P.replace('-', '').strip()
            if not num_part:
                return True
                
            # 如果已经设置了位数限制
            if self.digit_limit is not None:
                return len(num_part) <= self.digit_limit
        
        return True
        
    def create_input_fields(self, parent):
        # 创建坐标输入框架
        self.points_frame = ttk.LabelFrame(parent, text='坐标输入', padding="10")
        self.points_frame.pack(fill='x')
        
        self.entries = []
        points = ['A', 'B', 'C']
        
        for i, point in enumerate(points):
            frame = ttk.Frame(self.points_frame)
            frame.pack(pady=5)
            
            label = ttk.Label(frame, text=f'点{point}:', style='Input.TLabel')
            label.pack(side='left', padx=5)
            
            # 创建x坐标输入框（在(x,y)模式下用作坐标对输入）
            x_entry = ttk.Entry(frame, width=20, justify='center')
            x_entry.pack(side='left', padx=2)
            x_entry.bind('<FocusOut>', self.on_focus_out)
            x_entry.configure(validate='key', validatecommand=(self.window.register(self.validate_number), '%P', '%W'))
            
            # 创建y坐标输入框（在标准模式下使用）
            y_entry = ttk.Entry(frame, width=10, justify='center')
            y_entry.configure(validate='key', validatecommand=(self.window.register(self.validate_number), '%P', '%W'))
            
            # 创建x和y标签（在标准模式下使用）
            x_label = ttk.Label(frame, text='x =', style='Input.TLabel')
            y_label = ttk.Label(frame, text='y =', style='Input.TLabel')
            
            # 将组件添加到列表中
            self.entries.append((x_entry, y_entry))
            
        # 初始化显示模式
        self.update_input_display()
    
    def toggle_input_mode(self):
        # 切换输入模式时重置所有输入
        self.reset_inputs()
        # 更新输入框显示
        self.update_input_display()
    
    def update_input_display(self):
        # 更新输入框显示模式
        for i, (x_entry, y_entry) in enumerate(self.entries):
            frame = x_entry.master
            
            # 获取该行的所有组件并移除除了点标签之外的所有组件
            frame_children = frame.pack_slaves()
            for child in frame_children[1:]:
                child.pack_forget()
            
            if self.input_mode_var.get():
                # (x,y)格式模式
                x_entry.configure(width=20)
                x_entry.delete(0, tk.END)
                x_entry.pack(side='left', padx=2)
            else:
                # 标准模式
                x_entry.configure(width=10)
                # 创建并添加x标签和输入框
                x_label = ttk.Label(frame, text='x =', style='Input.TLabel')
                x_label.pack(side='left', padx=2)
                x_entry.pack(side='left', padx=2)
                # 创建并添加y标签和输入框
                y_label = ttk.Label(frame, text='y =', style='Input.TLabel')
                y_label.pack(side='left', padx=2)
                y_entry.pack(side='left', padx=2)
    
    def parse_coordinate(self, coord_str):
        # 解析(x,y)格式的坐标
        match = re.match(r'\((\-?\d+),(\-?\d+)\)', coord_str)
        if not match:
            raise ValueError("坐标格式错误，请使用(x,y)格式！")
        return float(match.group(1)), float(match.group(2))
    
    def calculate_circumcenter(self):
        try:
            # 获取输入的坐标
            points = []
            if self.input_mode_var.get():
                # (x,y)格式输入模式
                for x_entry, _ in self.entries:
                    coord_str = x_entry.get().strip()
                    if not coord_str:
                        raise ValueError("请填写所有坐标！")
                    x, y = self.parse_coordinate(coord_str)
                    points.append((x, y))
            else:
                # 分别输入x,y坐标模式
                for x_entry, y_entry in self.entries:
                    if not x_entry.get() or not y_entry.get():
                        raise ValueError("请填写所有坐标！")
                    x = float(x_entry.get())
                    y = float(y_entry.get())
                    points.append((x, y))
            
            # 解包坐标
            (x1, y1), (x2, y2), (x3, y3) = points
            
            # 检查三点是否共线
            area = abs((x1 * (y2 - y3) + x2 * (y3 - y1) + x3 * (y1 - y2)) / 2)
            if abs(area) < 1e-10:
                raise ValueError("这三个点共线，无法构成三角形！")
            
            # 计算外心坐标
            d = 2 * (x1 * (y2 - y3) + x2 * (y3 - y1) + x3 * (y1 - y2))
            
            x = ((x1*x1 + y1*y1) * (y2 - y3) + 
                 (x2*x2 + y2*y2) * (y3 - y1) + 
                 (x3*x3 + y3*y3) * (y1 - y2)) / d
            
            y = ((x1*x1 + y1*y1) * (x3 - x2) + 
                 (x2*x2 + y2*y2) * (x1 - x3) + 
                 (x3*x3 + y3*y3) * (x2 - x1)) / d
            
            # 显示结果
            self.result_label.config(
                text=f'外心坐标: ({x:.2f}, {y:.2f})'
            )
            
        except ValueError as e:
            messagebox.showerror('错误', str(e))
        except Exception as e:
            messagebox.showerror('错误', '请输入有效的数字！')
    
    def reset_inputs(self):
        # 清空所有输入框
        for x_entry, y_entry in self.entries:
            x_entry.delete(0, tk.END)
            y_entry.delete(0, tk.END)
        
        # 重置位数限制
        self.digit_limit = None
        
        # 重置结果显示
        self.result_label.config(text='外心坐标将在这里显示')
    
    def run(self):
        self.window.mainloop()

if __name__ == '__main__':
    try:
        app = CircumcenterCalculator()
        app.run()
    except Exception as e:
        messagebox.showerror('错误', f'程序运行失败：{str(e)}')