class Config:
    # RTSP配置
    RTSP_URL = "rtsp://your_camera_url"
    
    # 模型配置
    MODEL_PATH = "models/garbage_detector.pth"
    DEVICE = "cuda"  # 或 "cpu"
    CONFIDENCE_THRESHOLD = 0.5
    
    # 垃圾分类
    GARBAGE_CLASSES = [
        'plastic_bottle', 'glass_bottle', 'paper', 'cardboard',
        'metal_can', 'plastic_bag', 'food_waste', 'battery',
        'electronic_waste', 'clothing'
    ]
    
    # 垃圾类型对应的处理方法
    GARBAGE_TYPES = {
        'plastic_bottle': '可回收',
        'glass_bottle': '可回收',
        'paper': '可回收',
        'cardboard': '可回收',
        'metal_can': '可回收',
        'plastic_bag': '可回收',
        'food_waste': '厨余垃圾',
        'battery': '有害垃圾',
        'electronic_waste': '有害垃圾',
        'clothing': '可回收'
    }
    
    # 视频处理配置
    FRAME_WIDTH = 1280
    FRAME_HEIGHT = 720
    FPS = 30
    
    # 存储配置
    SAVE_DETECTION = True
    DETECTION_SAVE_PATH = "detections/"
    LOG_FILE = "logs/garbage_detection.log"
    
    # 数据库配置
    DB_HOST = "localhost"
    DB_NAME = "garbage_detection"
    DB_USER = "user"
    DB_PASSWORD = "password" 