垃圾检测系统源代码
====================

1. 主检测程序 (garbage_detection.py)
------------------
```python
import cv2
import numpy as np
import torch
import os
import logging
from datetime import datetime
from torchvision.models import detection
from config import Config
from database import Database

class GarbageDetector:
    def __init__(self):
        self._setup_logging()
        self._setup_directories()
        self._setup_model()
        self.db = Database()
        self.frame_count = 0
        self.detection_count = {}
        
    def _setup_logging(self):
        os.makedirs(os.path.dirname(Config.LOG_FILE), exist_ok=True)
        logging.basicConfig(
            filename=Config.LOG_FILE,
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        
    def _setup_directories(self):
        os.makedirs(Config.DETECTION_SAVE_PATH, exist_ok=True)
        
    def _setup_model(self):
        try:
            self.model = detection.fasterrcnn_resnet50_fpn(pretrained=True)
            self.model.eval()
            self.device = torch.device(Config.DEVICE if torch.cuda.is_available() else "cpu")
            self.model.to(self.device)
            logging.info(f"模型加载成功，使用设备: {self.device}")
        except Exception as e:
            logging.error(f"模型加载失败: {str(e)}")
            raise

    def process_frame(self, frame):
        try:
            frame = cv2.resize(frame, (Config.FRAME_WIDTH, Config.FRAME_HEIGHT))
            image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            image = torch.from_numpy(image.transpose((2, 0, 1))).float()
            image = image.unsqueeze(0)
            image = image.to(self.device)

            with torch.no_grad():
                predictions = self.model(image)

            return frame, predictions
        except Exception as e:
            logging.error(f"处理帧时出错: {str(e)}")
            return frame, None

    def save_detection_image(self, frame):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"detection_{timestamp}.jpg"
        filepath = os.path.join(Config.DETECTION_SAVE_PATH, filename)
        cv2.imwrite(filepath, frame)
        return filepath

    def draw_detections(self, frame, predictions):
        if predictions is None:
            return frame

        boxes = predictions[0]['boxes'].cpu().numpy()
        labels = predictions[0]['labels'].cpu().numpy()
        scores = predictions[0]['scores'].cpu().numpy()

        detections = []
        for box, label, score in zip(boxes, labels, scores):
            if score > Config.CONFIDENCE_THRESHOLD:
                x1, y1, x2, y2 = box.astype(int)
                garbage_type = Config.GARBAGE_CLASSES[label - 1]
                category = Config.GARBAGE_TYPES.get(garbage_type, '未分类')
                
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.putText(frame, f"{garbage_type}: {score:.2f}", 
                          (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 
                          0.9, (0, 255, 0), 2)
                cv2.putText(frame, f"类别: {category}", 
                          (x1, y1-30), cv2.FONT_HERSHEY_SIMPLEX,
                          0.7, (255, 0, 0), 2)
                
                detections.append((garbage_type, score))
                self.detection_count[garbage_type] = self.detection_count.get(garbage_type, 0) + 1

        return frame, detections

    def detect_garbage(self, rtsp_url=None):
        if rtsp_url is None:
            rtsp_url = Config.RTSP_URL

        cap = cv2.VideoCapture(rtsp_url)
        if not cap.isOpened():
            logging.error(f"无法打开视频流: {rtsp_url}")
            return

        logging.info(f"开始检测垃圾 - 视频流: {rtsp_url}")
        
        try:
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break

                self.frame_count += 1
                frame, predictions = self.process_frame(frame)
                frame, detections = self.draw_detections(frame, predictions)
                
                if detections and Config.SAVE_DETECTION:
                    image_path = self.save_detection_image(frame)
                    for garbage_type, confidence in detections:
                        self.db.add_detection(garbage_type, confidence, image_path)
                        self.db.update_statistics(garbage_type)
                
                self._draw_statistics(frame)
                cv2.imshow('Garbage Detection', frame)
                
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break

        except Exception as e:
            logging.error(f"检测过程中出错: {str(e)}")
        finally:
            cap.release()
            cv2.destroyAllWindows()
            self.db.close()
            self._log_session_summary()

    def _draw_statistics(self, frame):
        y_offset = 30
        for garbage_type, count in self.detection_count.items():
            text = f"{garbage_type}: {count}"
            cv2.putText(frame, text, (frame.shape[1] - 300, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
            y_offset += 20

    def _log_session_summary(self):
        logging.info("检测会话结束")
        logging.info(f"总处理帧数: {self.frame_count}")
        for garbage_type, count in self.detection_count.items():
            logging.info(f"{garbage_type} 检测次数: {count}")

if __name__ == "__main__":
    detector = GarbageDetector()
    detector.detect_garbage()
```

2. 训练程序 (train.py)
------------------
```python
import os
import torch
import torch.utils.data
from torch.utils.data import Dataset, DataLoader
import torchvision
from torchvision.models.detection import fasterrcnn_resnet50_fpn
from torchvision.models.detection.faster_rcnn import FastRCNNPredictor
import transforms as T
from PIL import Image
import numpy as np
from config import Config
import logging
from datetime import datetime

class GarbageDataset(Dataset):
    def __init__(self, root, transforms=None):
        self.root = root
        self.transforms = transforms
        self.imgs = list(sorted(os.listdir(os.path.join(root, "images"))))
        self.annotations = list(sorted(os.listdir(os.path.join(root, "annotations"))))

    def __getitem__(self, idx):
        img_path = os.path.join(self.root, "images", self.imgs[idx])
        ann_path = os.path.join(self.root, "annotations", self.annotations[idx])
        
        img = Image.open(img_path).convert("RGB")
        
        boxes = []
        labels = []
        with open(ann_path, 'r') as f:
            for line in f:
                values = list(map(float, line.strip().split()))
                labels.append(int(values[0]))
                boxes.append(values[1:])
        
        boxes = torch.as_tensor(boxes, dtype=torch.float32)
        labels = torch.as_tensor(labels, dtype=torch.int64)
        
        target = {}
        target["boxes"] = boxes
        target["labels"] = labels
        
        if self.transforms is not None:
            img, target = self.transforms(img, target)

        return img, target

    def __len__(self):
        return len(self.imgs)

def get_transform(train):
    transforms = []
    transforms.append(T.ToTensor())
    if train:
        transforms.append(T.RandomHorizontalFlip(0.5))
        transforms.append(T.RandomIoUCrop())
        transforms.append(T.RandomZoomOut())
        transforms.append(T.RandomPhotometricDistort())
    return T.Compose(transforms)

def get_model(num_classes):
    model = fasterrcnn_resnet50_fpn(pretrained=True)
    in_features = model.roi_heads.box_predictor.cls_score.in_features
    model.roi_heads.box_predictor = FastRCNNPredictor(in_features, num_classes)
    return model

def train_one_epoch(model, optimizer, data_loader, device, epoch):
    model.train()
    total_loss = 0
    
    for i, (images, targets) in enumerate(data_loader):
        images = list(image.to(device) for image in images)
        targets = [{k: v.to(device) for k, v in t.items()} for t in targets]

        loss_dict = model(images, targets)
        losses = sum(loss for loss in loss_dict.values())
        
        optimizer.zero_grad()
        losses.backward()
        optimizer.step()
        
        total_loss += losses.item()
        
        if i % 50 == 0:
            print(f"Epoch: {epoch}, Batch: {i}, Loss: {losses.item():.4f}")
    
    return total_loss / len(data_loader)

def evaluate(model, data_loader, device):
    model.eval()
    total_loss = 0
    
    with torch.no_grad():
        for images, targets in data_loader:
            images = list(image.to(device) for image in images)
            targets = [{k: v.to(device) for k, v in t.items()} for t in targets]
            
            loss_dict = model(images, targets)
            losses = sum(loss for loss in loss_dict.values())
            total_loss += losses.item()
    
    return total_loss / len(data_loader)

def main():
    device = torch.device(Config.DEVICE if torch.cuda.is_available() else "cpu")
    data_path = "dataset"
    
    dataset = GarbageDataset(
        os.path.join(data_path, "train"),
        get_transform(train=True)
    )
    dataset_test = GarbageDataset(
        os.path.join(data_path, "val"),
        get_transform(train=False)
    )

    data_loader = DataLoader(
        dataset, batch_size=2, shuffle=True,
        collate_fn=lambda x: tuple(zip(*x))
    )
    data_loader_test = DataLoader(
        dataset_test, batch_size=1, shuffle=False,
        collate_fn=lambda x: tuple(zip(*x))
    )

    num_classes = len(Config.GARBAGE_CLASSES) + 1
    model = get_model(num_classes)
    model.to(device)

    params = [p for p in model.parameters() if p.requires_grad]
    optimizer = torch.optim.SGD(params, lr=0.005, momentum=0.9, weight_decay=0.0005)
    lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=3, gamma=0.1)

    num_epochs = 10
    best_loss = float('inf')
    
    for epoch in range(num_epochs):
        train_loss = train_one_epoch(model, optimizer, data_loader, device, epoch)
        lr_scheduler.step()
        val_loss = evaluate(model, data_loader_test, device)
        
        print(f"Epoch {epoch} - Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
        
        if val_loss < best_loss:
            best_loss = val_loss
            torch.save(model.state_dict(), Config.MODEL_PATH)
            print(f"Saved best model with validation loss: {val_loss:.4f}")

if __name__ == "__main__":
    main()
```

3. 数据库操作 (database.py)
------------------
```python
import mysql.connector
from datetime import datetime
from config import Config

class Database:
    def __init__(self):
        self.connection = mysql.connector.connect(
            host=Config.DB_HOST,
            database=Config.DB_NAME,
            user=Config.DB_USER,
            password=Config.DB_PASSWORD
        )
        self.cursor = self.connection.cursor()
        self._create_tables()

    def _create_tables(self):
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS detection_records (
                id INT AUTO_INCREMENT PRIMARY KEY,
                timestamp DATETIME,
                garbage_type VARCHAR(50),
                confidence FLOAT,
                image_path VARCHAR(255),
                garbage_category VARCHAR(20)
            )
        ''')
        
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS statistics (
                id INT AUTO_INCREMENT PRIMARY KEY,
                date DATE,
                garbage_type VARCHAR(50),
                count INT,
                category VARCHAR(20)
            )
        ''')
        self.connection.commit()

    def add_detection(self, garbage_type, confidence, image_path):
        category = Config.GARBAGE_TYPES.get(garbage_type, '未分类')
        sql = '''
            INSERT INTO detection_records 
            (timestamp, garbage_type, confidence, image_path, garbage_category)
            VALUES (%s, %s, %s, %s, %s)
        '''
        values = (datetime.now(), garbage_type, confidence, image_path, category)
        self.cursor.execute(sql, values)
        self.connection.commit()

    def update_statistics(self, garbage_type):
        category = Config.GARBAGE_TYPES.get(garbage_type, '未分类')
        today = datetime.now().date()
        
        self.cursor.execute('''
            SELECT id, count FROM statistics 
            WHERE date = %s AND garbage_type = %s
        ''', (today, garbage_type))
        
        result = self.cursor.fetchone()
        if result:
            self.cursor.execute('''
                UPDATE statistics 
                SET count = count + 1 
                WHERE id = %s
            ''', (result[0],))
        else:
            self.cursor.execute('''
                INSERT INTO statistics (date, garbage_type, count, category)
                VALUES (%s, %s, %s, %s)
            ''', (today, garbage_type, 1, category))
        
        self.connection.commit()

    def get_daily_statistics(self, date=None):
        if date is None:
            date = datetime.now().date()
        
        self.cursor.execute('''
            SELECT garbage_type, count, category 
            FROM statistics 
            WHERE date = %s
        ''', (date,))
        
        return self.cursor.fetchall()

    def close(self):
        self.cursor.close()
        self.connection.close()
```

4. 数据转换模块 (transforms.py)
------------------
```python
import random
import torch
import torchvision.transforms as T
import torchvision.transforms.functional as F

class Compose:
    def __init__(self, transforms):
        self.transforms = transforms

    def __call__(self, image, target):
        for t in self.transforms:
            image, target = t(image, target)
        return image, target

class ToTensor:
    def __call__(self, image, target):
        image = F.to_tensor(image)
        return image, target

class RandomHorizontalFlip:
    def __init__(self, prob=0.5):
        self.prob = prob

    def __call__(self, image, target):
        if random.random() < self.prob:
            height, width = image.shape[-2:]
            image = F.hflip(image)
            if "boxes" in target:
                boxes = target["boxes"]
                boxes[:, [0, 2]] = width - boxes[:, [2, 0]]
                target["boxes"] = boxes
        return image, target

class RandomIoUCrop:
    def __init__(self, min_scale=0.3, max_scale=1.0):
        self.min_scale = min_scale
        self.max_scale = max_scale
        
    def __call__(self, image, target):
        w, h = F._get_image_size(image)
        
        for _ in range(10):
            scale = random.uniform(self.min_scale, self.max_scale)
            min_ar = 0.5
            max_ar = 2
            aspect_ratio = random.uniform(min_ar, max_ar)
            
            h_crop = int(scale * h)
            w_crop = int(aspect_ratio * h_crop)
            
            if w_crop > w or h_crop > h:
                continue
                
            i = random.randint(0, h - h_crop)
            j = random.randint(0, w - w_crop)
            
            image = F.crop(image, i, j, h_crop, w_crop)
            
            if "boxes" in target:
                boxes = target["boxes"]
                boxes[:, [0, 2]] = boxes[:, [0, 2]] - j
                boxes[:, [1, 3]] = boxes[:, [1, 3]] - i
                
                boxes[:, [0, 2]] = boxes[:, [0, 2]].clamp(0, w_crop)
                boxes[:, [1, 3]] = boxes[:, [1, 3]].clamp(0, h_crop)
                
                keep = (boxes[:, 3] > boxes[:, 1]) & (boxes[:, 2] > boxes[:, 0])
                boxes = boxes[keep]
                target["boxes"] = boxes
                if "labels" in target:
                    target["labels"] = target["labels"][keep]
                    
            return image, target
            
        return image, target

class RandomZoomOut:
    def __init__(self, fill=0):
        self.fill = fill
        
    def __call__(self, image, target):
        if random.random() < 0.5:
            return image, target
            
        height, width = F._get_image_size(image)
        scale = random.uniform(1, 2)
        h_new = int(height * scale)
        w_new = int(width * scale)
        
        image_new = torch.full((3, h_new, w_new), self.fill, dtype=torch.float32)
        
        i = random.randint(0, h_new - height)
        j = random.randint(0, w_new - width)
        
        image_new[:, i:i+height, j:j+width] = image
        
        if "boxes" in target:
            boxes = target["boxes"]
            boxes[:, [0, 2]] = boxes[:, [0, 2]] + j
            boxes[:, [1, 3]] = boxes[:, [1, 3]] + i
            target["boxes"] = boxes
            
        return image_new, target

class RandomPhotometricDistort:
    def __init__(self):
        self.pd = [
            T.ColorJitter(brightness=0.2),
            T.ColorJitter(contrast=0.2),
            T.ColorJitter(saturation=0.2),
            T.ColorJitter(hue=0.1)
        ]
        
    def __call__(self, image, target):
        if random.random() < 0.5:
            distort = random.choice(self.pd)
            image = distort(image)
        return image, target
``` 