import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

class Calculator:
    def __init__(self):
        try:
            self.window = tk.Tk()
            self.window.title('计算器')
            self.window.geometry('300x400')
            
            # 使窗口居中显示
            screen_width = self.window.winfo_screenwidth()
            screen_height = self.window.winfo_screenheight()
            x = (screen_width - 300) // 2
            y = (screen_height - 400) // 2
            self.window.geometry(f'300x400+{x}+{y}')
            
            # 设置最小窗口大小
            self.window.minsize(250, 350)
            
            # 显示框
            self.display = ttk.Entry(self.window, justify='right', font=('Arial', 20))
            self.display.grid(row=0, column=0, columnspan=4, padx=5, pady=5, sticky='nsew')
            
            # 按钮文本
            button_texts = [
                '7', '8', '9', '/',
                '4', '5', '6', '*',
                '1', '2', '3', '-',
                '0', '.', '=', '+',
                'C'
            ]
            
            # 创建并放置按钮
            row = 1
            col = 0
            for text in button_texts:
                if text == 'C':
                    button = ttk.Button(self.window, text=text, command=lambda t=text: self.clear())
                    button.grid(row=5, column=0, columnspan=4, sticky='nsew', padx=2, pady=2)
                else:
                    button = ttk.Button(self.window, text=text, command=lambda t=text: self.click(t))
                    button.grid(row=row, column=col, sticky='nsew', padx=2, pady=2)
                    col += 1
                    if col > 3:
                        col = 0
                        row += 1
            
            # 设置网格权重
            for i in range(6):
                self.window.grid_rowconfigure(i, weight=1)
            for i in range(4):
                self.window.grid_columnconfigure(i, weight=1)
            
        except Exception as e:
            tk.messagebox.showerror('错误', f'程序初始化失败：{str(e)}')
            sys.exit(1)
    
    def click(self, text):
        if text == '=':
            try:
                # 获取表达式
                expr = self.display.get()
                # 安全检查
                if any(keyword in expr for keyword in ['import', 'exec', 'eval', 'os', 'sys']):
                    raise ValueError('非法表达式')
                result = eval(expr)
                self.display.delete(0, tk.END)
                self.display.insert(tk.END, str(result))
            except Exception as e:
                self.display.delete(0, tk.END)
                self.display.insert(tk.END, '错误')
        else:
            self.display.insert(tk.END, text)
    
    def clear(self):
        self.display.delete(0, tk.END)
    
    def run(self):
        self.window.mainloop()

if __name__ == '__main__':
    try:
        if getattr(sys, 'frozen', False):
            # 如果是打包后的exe运行
            os.chdir(os.path.dirname(sys.executable))
        calc = Calculator()
        calc.run()
    except Exception as e:
        tk.messagebox.showerror('错误', f'程序运行失败：{str(e)}') 