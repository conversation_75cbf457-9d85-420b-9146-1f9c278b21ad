你好！新的NAS信息如下：
一、192.168.161.16NAS目录： ipv4.nfs.oceanstor9000-126.com:/YSFW_01
挂载方法如下：
1、配置DNS：************
2、安装nfs：yum install -y nfs-utils  --如已安装则忽略
3、挂载：
mkdir /data --按实际需要创建挂载目录
mount -t nfs -o vers=3,timeo=600,nolock ipv4.nfs.oceanstor9000-126.com:/YSFW_01  /data
4、配置fstab设置自动挂载
ipv4.nfs.oceanstor9000-126.com:/YSFW_01   /data   nfs     defaults,nofail        0 0

二、192.168.161.17NAS目录： ipv4.nfs.oceanstor9000-126.com:/YSFW_02
挂载方法如下：
1、配置DNS：************
2、安装nfs：yum install -y nfs-utils  --如已安装则忽略
3、挂载：
mkdir /data --按实际需要创建挂载目录
mount -t nfs -o vers=3,timeo=600,nolock ipv4.nfs.oceanstor9000-126.com:/YSFW_02  /data
4、配置fstab设置自动挂载
ipv4.nfs.oceanstor9000-126.com:/YSFW_02   /data   nfs     defaults,nofail        0 0

三、192.168.161.20NAS目录： ipv4.nfs.oceanstor9000-126.com:/YSFW_03
挂载方法如下：
1、配置DNS：************
2、安装nfs：yum install -y nfs-utils  --如已安装则忽略
3、挂载：
mkdir /datacc --按实际需要创建挂载目录
mount -t nfs -o vers=3,timeo=600,nolock ipv4.nfs.oceanstor9000-126.com:/YSFW_03  /datacc2
4、配置fstab设置自动挂载
ipv4.nfs.oceanstor9000-126.com:/YSFW_03   /data   nfs     defaults,nofail        0 0

四、192.168.161.21NAS目录： ipv4.nfs.oceanstor9000-126.com:/YSFW
挂载方法如下：
1、配置DNS：************
2、安装nfs：yum install -y nfs-utils  --如已安装则忽略
3、挂载：
mkdir /data --按实际需要创建挂载目录
mount -t nfs -o vers=3,timeo=600,nolock ipv4.nfs.oceanstor9000-126.com:/YSFW  /datacc
4、配置fstab设置自动挂载
ipv4.nfs.oceanstor9000-126.com:/YSFW   /data   nfs     defaults,nofail        0 0


rpm -qa | grep nfs-utils

vi /etc/sysconfig/network-scripts/ifcfg
DNS2=************
systemctl restart NetworkManager

 
umount /datacc
rpm -qa |grep rsync
 
rsync -avh /home/<USER>/ /home/<USER>