#!/bin/bash

# 配置项
JAVA_HOME=/home/<USER>/tools/jdk/java8/
APP_NAME="v.jar"
LOG_DIR="/root/system_monitor"
#DATE=$(date +%Y%m%d)
DATE=$(date +%Y%m%d%H%M)
LOG_FILE="$LOG_DIR/java_monitor_${DATE}.log"
HEAP_DUMP_DIR="$LOG_DIR/heapdump"

# 创建日志目录
mkdir -p $LOG_DIR $HEAP_DUMP_DIR

# 获取Java进程PID
get_pid() {
    pid=$(ps -ef | grep "$APP_NAME" | grep -v grep | awk '{print $2}')
    echo $pid
}

# 记录日志的函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# 检查Java进程
check_process() {
    pid=$(get_pid)
    if [ -z "$pid" ]; then
        log "错误：未找到 $APP_NAME 的进程"
        return 1
    fi
    log "检查进程 PID: $pid"
    return 0
}

# 检查线程状态
check_threads() {
    pid=$(get_pid)
    log "========== 线程状态检查 =========="
    
    # 获取线程数量
    thread_count=$($JAVA_HOME/bin/jstack $pid | grep "Thread" | wc -l)
    log "总线程数: $thread_count"
    
    # 检查死锁
    deadlock=$($JAVA_HOME/bin/jstack $pid | grep "deadlock" -A 10)
    if [ ! -z "$deadlock" ]; then
        log "警告：检测到死锁！"
        log "$deadlock"
    fi
    
    # 获取线程状态统计
    log "线程状态统计："
    $JAVA_HOME/bin/jstack $pid | grep "java.lang.Thread.State" | sort | uniq -c | while read count state
    do
        log "  $state: $count"
    done
    
    # 获取前10个CPU使用最高的线程
    log "CPU使用率最高的前10个线程："
    top_threads=$($JAVA_HOME/bin/jstack $pid | grep "nid=0x" -A 1 | head -20)
    log "$top_threads"
}

# 检查内存状态
check_memory() {
    pid=$(get_pid)
    log "========== 内存状态检查 =========="
    
    # 获取堆内存使用情况
    heap_info=$($JAVA_HOME/bin/jmap -heap $pid)
    log "堆内存信息："
    log "$heap_info"
    
    # 获取内存对象统计
    log "内存对象统计TOP 20："
    $JAVA_HOME/bin/jmap -histo $pid | head -23 >> "$LOG_FILE"
    
    # 检查老年代使用率
    old_gen_usage=$($JAVA_HOME/bin/jstat -gcutil $pid | tail -n 1 | awk '{print $3}')
    if (( $(echo "$old_gen_usage > 80" | bc -l) )); then
        log "警告：老年代使用率超过80%: $old_gen_usage%"
        
        # 如果老年代使用率过高，生成堆转储文件
        dump_file="$HEAP_DUMP_DIR/heap_dump_${DATE}_${pid}.hprof"
        log "生成堆转储文件: $dump_file"
        $JAVA_HOME/bin/jmap -dump:format=b,file=$dump_file $pid
    fi
    
    # 检查GC情况
    gc_info=$($JAVA_HOME/bin/jstat -gcutil $pid)
    log "GC统计信息："
    log "$gc_info"
}

# 主函数
main() {
    log "开始检查Java进程状态..."
    
    if ! check_process; then
        return 1
    fi
    
    # 检查线程状态
    check_threads
    
    # 检查内存状态
    check_memory
    
    log "检查完成\n"
}

# 执行主函数
main
