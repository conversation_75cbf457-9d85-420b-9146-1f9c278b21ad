import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.RecursiveAction;

/**
 * 目前最快的排序算法实现集合
 * 包含：双轴快排、并行排序、Timsort、基数排序
 */
public class FastSortingAlgorithms {
    
    // 1. 双轴快速排序 (Dual-Pivot Quicksort) - Java Arrays.sort() 使用的算法
    public static void dualPivotQuickSort(int[] arr) {
        dualPivotQuickSort(arr, 0, arr.length - 1);
    }
    
    private static void dualPivotQuickSort(int[] arr, int left, int right) {
        if (right - left < 27) { // 小数组使用插入排序
            insertionSort(arr, left, right);
            return;
        }
        
        // 选择两个轴点
        if (arr[left] > arr[right]) {
            swap(arr, left, right);
        }
        
        int pivot1 = arr[left];
        int pivot2 = arr[right];
        
        int less = left + 1;
        int great = right - 1;
        
        for (int k = less; k <= great; k++) {
            if (arr[k] < pivot1) {
                swap(arr, k, less++);
            } else if (arr[k] > pivot2) {
                while (k < great && arr[great] > pivot2) {
                    great--;
                }
                swap(arr, k, great--);
                if (arr[k] < pivot1) {
                    swap(arr, k, less++);
                }
            }
        }
        
        swap(arr, left, --less);
        swap(arr, right, ++great);
        
        // 递归排序三个部分
        dualPivotQuickSort(arr, left, less - 1);
        if (pivot1 != pivot2) {
            dualPivotQuickSort(arr, less + 1, great - 1);
        }
        dualPivotQuickSort(arr, great + 1, right);
    }
    
    // 2. 并行快速排序 (使用 ForkJoin 框架)
    public static void parallelQuickSort(int[] arr) {
        ForkJoinPool pool = new ForkJoinPool();
        pool.invoke(new ParallelQuickSortTask(arr, 0, arr.length - 1));
        pool.shutdown();
    }
    
    static class ParallelQuickSortTask extends RecursiveAction {
        private static final int THRESHOLD = 1000; // 阈值，小于此值使用串行排序
        private int[] arr;
        private int left, right;
        
        public ParallelQuickSortTask(int[] arr, int left, int right) {
            this.arr = arr;
            this.left = left;
            this.right = right;
        }
        
        @Override
        protected void compute() {
            if (right - left < THRESHOLD) {
                dualPivotQuickSort(arr, left, right);
                return;
            }
            
            int pivot = partition(arr, left, right);
            
            ParallelQuickSortTask leftTask = new ParallelQuickSortTask(arr, left, pivot - 1);
            ParallelQuickSortTask rightTask = new ParallelQuickSortTask(arr, pivot + 1, right);
            
            invokeAll(leftTask, rightTask);
        }
        
        private int partition(int[] arr, int left, int right) {
            int pivot = arr[right];
            int i = left - 1;
            
            for (int j = left; j < right; j++) {
                if (arr[j] <= pivot) {
                    swap(arr, ++i, j);
                }
            }
            swap(arr, i + 1, right);
            return i + 1;
        }
    }
    
    // 3. 基数排序 (对于整数数组非常快)
    public static void radixSort(int[] arr) {
        if (arr.length == 0) return;
        
        // 找到最大值确定位数
        int max = Arrays.stream(arr).max().orElse(0);
        
        // 对每一位进行计数排序
        for (int exp = 1; max / exp > 0; exp *= 10) {
            countingSortByDigit(arr, exp);
        }
    }
    
    private static void countingSortByDigit(int[] arr, int exp) {
        int n = arr.length;
        int[] output = new int[n];
        int[] count = new int[10];
        
        // 计算每个数字的出现次数
        for (int i = 0; i < n; i++) {
            count[(arr[i] / exp) % 10]++;
        }
        
        // 计算累积计数
        for (int i = 1; i < 10; i++) {
            count[i] += count[i - 1];
        }
        
        // 构建输出数组
        for (int i = n - 1; i >= 0; i--) {
            output[count[(arr[i] / exp) % 10] - 1] = arr[i];
            count[(arr[i] / exp) % 10]--;
        }
        
        // 复制回原数组
        System.arraycopy(output, 0, arr, 0, n);
    }
    
    // 4. 混合排序 (Introsort) - 结合快排、堆排序和插入排序
    public static void introSort(int[] arr) {
        int maxDepth = (int) (2 * Math.log(arr.length) / Math.log(2));
        introSort(arr, 0, arr.length - 1, maxDepth);
    }
    
    private static void introSort(int[] arr, int left, int right, int maxDepth) {
        if (right - left < 16) {
            insertionSort(arr, left, right);
        } else if (maxDepth == 0) {
            heapSort(arr, left, right);
        } else {
            int pivot = partition(arr, left, right);
            introSort(arr, left, pivot - 1, maxDepth - 1);
            introSort(arr, pivot + 1, right, maxDepth - 1);
        }
    }
    
    // 辅助方法
    private static void insertionSort(int[] arr, int left, int right) {
        for (int i = left + 1; i <= right; i++) {
            int key = arr[i];
            int j = i - 1;
            while (j >= left && arr[j] > key) {
                arr[j + 1] = arr[j];
                j--;
            }
            arr[j + 1] = key;
        }
    }
    
    private static void heapSort(int[] arr, int left, int right) {
        int n = right - left + 1;
        // 构建最大堆
        for (int i = n / 2 - 1; i >= 0; i--) {
            heapify(arr, left, n, i);
        }
        // 逐个提取元素
        for (int i = n - 1; i > 0; i--) {
            swap(arr, left, left + i);
            heapify(arr, left, i, 0);
        }
    }
    
    private static void heapify(int[] arr, int offset, int n, int i) {
        int largest = i;
        int left = 2 * i + 1;
        int right = 2 * i + 2;
        
        if (left < n && arr[offset + left] > arr[offset + largest]) {
            largest = left;
        }
        if (right < n && arr[offset + right] > arr[offset + largest]) {
            largest = right;
        }
        if (largest != i) {
            swap(arr, offset + i, offset + largest);
            heapify(arr, offset, n, largest);
        }
    }
    
    private static int partition(int[] arr, int left, int right) {
        int pivot = arr[right];
        int i = left - 1;
        for (int j = left; j < right; j++) {
            if (arr[j] <= pivot) {
                swap(arr, ++i, j);
            }
        }
        swap(arr, i + 1, right);
        return i + 1;
    }
    
    private static void swap(int[] arr, int i, int j) {
        int temp = arr[i];
        arr[i] = arr[j];
        arr[j] = temp;
    }
    
    // 性能测试方法
    public static void performanceTest() {
        int[] sizes = {1000, 10000, 100000, 1000000};
        
        for (int size : sizes) {
            System.out.println("\n=== 数组大小: " + size + " ===");
            
            // 测试各种排序算法
            testSortingAlgorithm("双轴快排", size, FastSortingAlgorithms::dualPivotQuickSort);
            testSortingAlgorithm("并行快排", size, FastSortingAlgorithms::parallelQuickSort);
            testSortingAlgorithm("基数排序", size, FastSortingAlgorithms::radixSort);
            testSortingAlgorithm("混合排序", size, FastSortingAlgorithms::introSort);
            testSortingAlgorithm("Java内置", size, Arrays::sort);
        }
    }
    
    private static void testSortingAlgorithm(String name, int size, SortingAlgorithm algorithm) {
        int[] arr = generateRandomArray(size);
        
        long startTime = System.nanoTime();
        algorithm.sort(arr.clone());
        long endTime = System.nanoTime();
        
        double timeMs = (endTime - startTime) / 1_000_000.0;
        System.out.printf("%-10s: %.2f ms\n", name, timeMs);
    }
    
    private static int[] generateRandomArray(int size) {
        Random random = new Random(42); // 固定种子确保可重复性
        return random.ints(size, 0, size * 10).toArray();
    }
    
    @FunctionalInterface
    public interface SortingAlgorithm {
        void sort(int[] arr);
    }
    
    // 主方法 - 演示和测试
    public static void main(String[] args) {
        System.out.println("🚀 最快排序算法演示");
        
        // 小规模测试
        int[] testArray = {64, 34, 25, 12, 22, 11, 90, 5, 77, 30};
        System.out.println("原数组: " + Arrays.toString(testArray));
        
        int[] arr1 = testArray.clone();
        dualPivotQuickSort(arr1);
        System.out.println("双轴快排: " + Arrays.toString(arr1));
        
        int[] arr2 = testArray.clone();
        parallelQuickSort(arr2);
        System.out.println("并行快排: " + Arrays.toString(arr2));
        
        int[] arr3 = testArray.clone();
        radixSort(arr3);
        System.out.println("基数排序: " + Arrays.toString(arr3));
        
        int[] arr4 = testArray.clone();
        introSort(arr4);
        System.out.println("混合排序: " + Arrays.toString(arr4));
        
        // 性能测试
        System.out.println("\n📊 性能测试开始...");
        performanceTest();
    }
}
