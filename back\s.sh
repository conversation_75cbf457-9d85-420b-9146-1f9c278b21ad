#!/bin/sh
# ./ry.sh start 启动 stop 停止 restart 重启 status 状态
AppName=cdaid.jar
export JAVA_HOME=/home/<USER>/tools/jdk/jdk18
export PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:$JAVA_HOME/bin
# JVM参数
JVM_OPTS="-Dname=$AppName  -Duser.timezone=Asia/Shanghai -Xms1024m -Xmx2048m -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -XX:+PrintGCDateStamps  -XX:+PrintGCDetails -XX:NewRatio=1 -XX:SurvivorRatio=30 -XX:+UseParallelGC -XX:+UseParallelOldGC"
APP_HOME=/home/<USER>/pro/back
LOG_PATH=$APP_HOME/logs/$AppName.log

if [ "$1" = "" ];
then
    echo -e "\033[0;31m 未输入操作名 \033[0m  \033[0;34m {start|stop|restart|status} \033[0m"
    exit 1
fi

if [ "$AppName" = "" ];
then
    echo -e "\033[0;31m 未输入应用名 \033[0m"
    exit 1
fi

function start()
{
    PID=$(/usr/bin/ps -ef | /usr/bin/grep java | /usr/bin/grep $AppName | /usr/bin/grep -v grep | /usr/bin/awk '{print $2}')

    if [ x"$PID" != x"" ]; then
        echo "$AppName is running..."
    else
        echo "Starting $AppName..."
        /usr/bin/mkdir -p $APP_HOME/logs
        nohup $JAVA_HOME/bin/java $JVM_OPTS -jar $APP_HOME/$AppName > $LOG_PATH 2>&1 &
        echo "Start $AppName success..."
    fi
}

function stop()
{
    echo "Stop $AppName"

    PID=""
    query(){
        PID=$(/usr/bin/ps -ef | /usr/bin/grep java | /usr/bin/grep $AppName | /usr/bin/grep -v grep | /usr/bin/awk '{print $2}')
    }

    query
    if [ x"$PID" != x"" ]; then
        echo "Stopping $AppName (pid:$PID)..."
        /bin/kill -TERM $PID
        echo "$AppName (pid:$PID) exiting..."
        while [ x"$PID" != x"" ]
        do
            sleep 1
            query
        done
        echo "$AppName exited."
    else
        echo "$AppName already stopped."
    fi
}

function restart()
{
    stop
    sleep 2
    start
}

function status()
{
    PID=`ps -ef |grep java|grep $AppName|grep -v grep|wc -l`
    if [ $PID != 0 ];then
        echo "$AppName is running..."
    else
        echo "$AppName is not running..."
    fi
}

case $1 in
    start)
    start;;
    stop)
    stop;;
    restart)
    restart;;
    status)
    status;;
    *)
    echo -e "\033[0;31m 未知操作 \033[0m  \033[0;34m {start|stop|restart|status} \033[0m"
    exit 1
esac
