[Unit]
Description=CDAID Spring Boot Application
# 在网络和系统基础服务启动后启动
After=network.target network-online.target syslog.target
# 添加其他依赖的服务，比如数据库等
After=mysql.service redis.service
# 确保网络已经准备就绪
Wants=network-online.target

[Service]
Type=forking
# 修改为实际的工作目录
WorkingDirectory=/home/<USER>/pro/back
# 使用 root 用户运行，因为当前是用 root 执行
User=root
# 设置完整的环境变量
Environment="JAVA_HOME=/home/<USER>/tools/jdk/jdk18"
Environment="PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/tools/jdk/jdk18/bin"
# 添加标准输出和错误输出的日志
StandardOutput=append:/var/log/cdaid.log
StandardError=append:/var/log/cdaid.error.log
# 使用完整路径执行命令
ExecStart=/bin/sh /home/<USER>/pro/back/s.sh start
# 停止命令
ExecStop=/bin/sh /home/<USER>/pro/back/s.sh stop
# 重启命令
ExecRestart=/bin/sh /home/<USER>/pro/back/s.sh restart
# 添加重启策略
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target