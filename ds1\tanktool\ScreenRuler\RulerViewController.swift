import UIKit

class RulerViewController: UIViewController {
    
    private var startPoint: CGPoint?
    private var endPoint: CGPoint?
    private var lineLayer: CAShapeLayer?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .clear
        
        // 添加手势识别
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        view.addGestureRecognizer(panGesture)
    }
    
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        let location = gesture.location(in: view)
        
        switch gesture.state {
        case .began:
            startPoint = location
            drawLine(from: location, to: location)
            
        case .changed:
            endPoint = location
            updateLine(to: location)
            
        case .ended, .cancelled:
            showMeasurement()
            
        default:
            break
        }
    }
    
    private func drawLine(from: CGPoint, to: CGPoint) {
        lineLayer?.removeFromSuperlayer()
        
        let path = UIBezierPath()
        path.move(to: from)
        path.addLine(to: to)
        
        let shapeLayer = CAShapeLayer()
        shapeLayer.path = path.cgPath
        shapeLayer.strokeColor = UIColor.red.cgColor
        shapeLayer.lineWidth = 2
        shapeLayer.lineDashPattern = [4, 4]
        
        view.layer.addSublayer(shapeLayer)
        lineLayer = shapeLayer
    }
    
    private func updateLine(to point: CGPoint) {
        guard let start = startPoint else { return }
        drawLine(from: start, to: point)
    }
    
    private func showMeasurement() {
        guard let start = startPoint, let end = endPoint else { return }
        
        let distance = hypot(end.x - start.x, end.y - start.y)
        let measurement = String(format: "%.1f pt", distance)
        
        let label = UILabel()
        label.text = measurement
        label.textColor = .white
        label.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 14)
        label.sizeToFit()
        label.frame.origin = CGPoint(x: end.x + 10, y: end.y + 10)
        
        view.addSubview(label)
    }
}