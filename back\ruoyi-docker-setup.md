# RuoYi 前后端分离项目 Docker Compose 部署指南

本文档提供了使用 Docker Compose 部署 RuoYi 前后端分离项目的详细步骤。

## 1. 准备目录结构

在您的项目根目录下创建以下目录和文件：

```
project_root/
├── front/
│   ├── dist/  (前端构建后的文件)
│   └── nginx.conf
├── back/
│   └── cdaid.jar (后端 JAR 文件)
├── db_init/
│   └── cdaid.sql (数据库初始化脚本)
├── Dockerfile-ruoyi-frontend
├── Dockerfile-ruoyi-backend
├── docker-compose-ruoyi.yaml
└── .env
```

-   **front/**: 存放前端相关文件。
    -   **dist/**: 前端项目构建后的文件（例如，Vue.js 项目的 `dist` 目录）。
    -   **nginx.conf**: 前端 Nginx 配置文件。
-   **back/**: 存放后端相关文件。
    -   **cdaid.jar**: 后端 Spring Boot 项目构建后的 JAR 文件。
-   **db_init/**: 存放数据库初始化脚本。
    -   **cdaid.sql**: 数据库初始化 SQL 脚本。
-   **Dockerfile-ruoyi-frontend**: 构建前端镜像的 Dockerfile。
-   **Dockerfile-ruoyi-backend**: 构建后端镜像的 Dockerfile。
-   **docker-compose-ruoyi.yaml**: Docker Compose 配置文件。
-   **.env**: 环境变量配置文件。

## 2. 构建镜像

### 2.1 构建前端镜像

1.  将 `Dockerfile-ruoyi-frontend` 和 `nginx.conf` 文件复制到 `front` 目录的同级目录。
2.  打开终端，进入包含 `Dockerfile-ruoyi-frontend` 的目录。
3.  运行以下命令构建前端镜像：

    ```bash
    docker build -t rouyifrontend:latest -f Dockerfile-ruoyi-frontend .
    ```

### 2.2 构建后端镜像

1.  将 `Dockerfile-ruoyi-backend` 文件复制到 `back` 目录的同级目录。
2.  打开终端，进入包含 `Dockerfile-ruoyi-backend` 的目录。
3.  (可选) 如果存在旧的后端镜像，先删除旧镜像：
    ```bash
    docker rmi rouyibackend:latest
    ```
4.  运行以下命令构建后端镜像：

    ```bash
    docker build -t rouyibackend:latest -f Dockerfile-ruoyi-backend .
    ```

## 3. 运行 Docker Compose

1.  将 `docker-compose-ruoyi.yaml` 和 `.env` 文件放在与 `front`、`back` 和 `db_init` 目录同级的位置。
2.  打开终端，进入包含 `docker-compose-ruoyi.yaml` 和 `.env` 文件的目录。
3.  (可选) 删除旧的容器、网络和数据卷（如果存在）：

    ```bash
    docker-compose -f docker-compose-ruoyi.yaml down -v
    ```
   如果使用了命名volume, 并且需要重新初始化数据库, 执行:
   ```bash
   docker volume rm ruoyi-mysql-data
   ```

4.  运行以下命令启动服务：

    ```bash
    docker-compose -f docker-compose-ruoyi.yaml up -d
    ```

## 4. 查看和管理容器

-   查看所有容器的状态：

    ```bash
    docker-compose -f docker-compose-ruoyi.yaml ps
    ```

-   查看后端容器的日志：

    ```bash
    docker-compose -f docker-compose-ruoyi.yaml logs -f ruoyi-backend
    ```

-   查看 MySQL 容器的日志：

    ```bash
    docker-compose -f docker-compose-ruoyi.yaml logs -f ruoyi-mysql
    ```
- 查看 Redis 容器的日志：
    ```bash
    docker-compose -f docker-compose-ruoyi.yaml logs -f ruoyi-redis
    ```

-   停止服务：

    ```bash
    docker-compose -f docker-compose-ruoyi.yaml down
    ```

## 5. 其他

- 查看docker compose 版本
    ```bash
    docker-compose --version
    ```
- 查看当前目录
    ```bash
    pwd