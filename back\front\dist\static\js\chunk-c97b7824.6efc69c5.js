(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c97b7824"],{6821:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[n("el-form-item",{attrs:{label:"区库",prop:"deptId"}},[n("el-select",{attrs:{placeholder:"请选择区库",clearable:"",filterable:""},on:{change:e.handleQuery},model:{value:e.queryParams.deptId,callback:function(t){e.$set(e.queryParams,"deptId",t)},expression:"queryParams.deptId"}},e._l(e.deptOptions,(function(e,t){return n("el-option",{key:t,attrs:{label:e.text,value:e.value}})})),1)],1),n("el-form-item",{attrs:{label:"名称",prop:"name"}},[n("el-input",{attrs:{placeholder:"请输入名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),n("el-form-item",{attrs:{label:"状态",prop:"status"}},[n("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.event_status,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),n("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),n("el-row",{staticClass:"mb8",attrs:{gutter:10}},[n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:event:add"],expression:"['asset:event:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),n("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.eventList},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{label:"区库",align:"center",prop:"deptName"}}),n("el-table-column",{attrs:{label:"名称",align:"center",prop:"name"}}),n("el-table-column",{attrs:{label:"类型",align:"center",prop:"type"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("dict-tag",{attrs:{options:e.dict.type.event_type,value:t.row.type}})]}}])}),n("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("dict-tag",{attrs:{options:e.dict.type.event_status,value:t.row.status}})]}}])}),n("el-table-column",{attrs:{label:"创建人",align:"center",prop:"createUser"}}),n("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),n("el-table-column",{attrs:{label:"审批人",align:"center",prop:"updateUser"}}),n("el-table-column",{attrs:{label:"审批时间",align:"center",prop:"updateTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e.parseTime(t.row.updateTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),n("el-table-column",{attrs:{label:"原因",align:"center",prop:"remark"}}),n("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return["0"==t.row.status?n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:event:edit"],expression:"['asset:event:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(n){return e.handleUpdate(t.row)}}},[e._v("修改 ")]):e._e(),"0"==t.row.status?n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:event:remove"],expression:"['asset:event:remove']"}],staticStyle:{color:"#ff0000"},attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除 ")]):e._e()]}}])})],1),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),n("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"区库",prop:"deptId"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择区库",clearable:"",filterable:""},model:{value:e.form.deptId,callback:function(t){e.$set(e.form,"deptId",t)},expression:"form.deptId"}},e._l(e.deptOptions,(function(e,t){return n("el-option",{key:t,attrs:{label:e.text,value:parseInt(e.value)}})})),1)],1),n("el-form-item",{attrs:{label:"名称",prop:"name"}},[n("el-input",{attrs:{placeholder:"请输入名称",maxlength:"50","show-word-limit":""},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),n("el-form-item",{attrs:{label:"类型",prop:"type"}},[n("el-radio-group",{attrs:{disabled:e.form.id},model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},e._l(e.dict.type.event_type,(function(t){return n("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label)+" ")])})),1)],1),n("el-form-item",{attrs:{label:"原因",prop:"remark"}},[n("el-input",{attrs:{type:"textarea",placeholder:"请输入原因",maxlength:"200","show-word-limit":""},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),n("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},r=[],s=n("5530"),i=(n("d81d"),n("14d9"),n("4de4"),n("d3b7"),n("b775"));function l(e){return Object(i["a"])({url:"/asset/event/list",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/asset/event/"+e,method:"get"})}function u(e){return Object(i["a"])({url:"/asset/event",method:"post",data:e})}function c(e){return Object(i["a"])({url:"/asset/event",method:"put",data:e})}function d(e){return Object(i["a"])({url:"/asset/event/"+e,method:"delete"})}var m=n("fcb7"),p=n("ac81"),f={name:"Event",dicts:["event_status","event_type"],data:function(){return{loading:!0,ids:[],checkedAssetEventItem:[],single:!0,multiple:!0,showSearch:!0,total:0,eventList:[],deptOptions:[],assetOptions:[],assetEventItemList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,deptId:null,name:null,status:null},form:{},rules:{deptId:[{required:!0,message:"区库不能为空",trigger:"blur"}],name:[{required:!0,message:"名称不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}],type:[{required:!0,message:"类型不能为空",trigger:"change"}],remark:[{required:!0,message:"原因不能为空",trigger:"blur"}]}}},created:function(){this.getList(),this.getDeptOption()},methods:{getList:function(){var e=this;this.loading=!0,l(this.queryParams).then((function(t){e.eventList=t.rows,e.total=t.total,e.loading=!1}))},getDeptOption:function(){var e=this;Object(m["f"])({type:"1"}).then((function(t){e.deptOptions=t.data}))},getAssetOption:function(){var e=this;this.assetOptions=[],Object(p["e"])({deptId:this.form.deptId}).then((function(t){e.assetOptions=t.data||[]}))},changeDept:function(){this.getAssetOption()},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,deptId:null,name:null,type:"0",status:"0",createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.assetEventItemList=[],this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加审批管理"},handleUpdate:function(e){var t=this;this.reset();var n=e.id||this.ids;o(n).then((function(e){t.form=e.data,t.assetEventItemList=e.data.assetEventItemList,t.open=!0,t.title="修改审批管理"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(e.form.assetEventItemList=e.assetEventItemList,null!=e.form.id?c(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,n=e.id||this.ids;this.$modal.confirm('是否确认删除审批管理编号为"'+n+'"的数据项？').then((function(){return d(n)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},rowAssetEventItemIndex:function(e){var t=e.row,n=e.rowIndex;t.index=n+1},handleAddAssetEventItem:function(){var e={assetId:"",num:"",remark:""};this.assetEventItemList.push(e)},handleDeleteAssetEventItem:function(){if(0==this.checkedAssetEventItem.length)this.$modal.msgError("请先选择要删除的审批项数据");else{var e=this.assetEventItemList,t=this.checkedAssetEventItem;this.assetEventItemList=e.filter((function(e){return-1==t.indexOf(e.index)}))}},handleAssetEventItemSelectionChange:function(e){this.checkedAssetEventItem=e.map((function(e){return e.index}))},handleExport:function(){this.download("asset/event/export",Object(s["a"])({},this.queryParams),"event_".concat((new Date).getTime(),".xlsx"))}}},h=f,v=n("2877"),b=Object(v["a"])(h,a,r,!1,null,null,null);t["default"]=b.exports},ac81:function(e,t,n){"use strict";n.d(t,"d",(function(){return r})),n.d(t,"f",(function(){return s})),n.d(t,"e",(function(){return i})),n.d(t,"c",(function(){return l})),n.d(t,"a",(function(){return o})),n.d(t,"g",(function(){return u})),n.d(t,"b",(function(){return c}));var a=n("b775");function r(e){return Object(a["a"])({url:"/asset/info/list",method:"get",params:e})}function s(e){return Object(a["a"])({url:"/asset/info/option",method:"get",params:e})}function i(e){return Object(a["a"])({url:"/asset/info/optionByDept",method:"get",params:e})}function l(e){return Object(a["a"])({url:"/asset/info/"+e,method:"get"})}function o(e){return Object(a["a"])({url:"/asset/info",method:"post",data:e})}function u(e){return Object(a["a"])({url:"/asset/info",method:"put",data:e})}function c(e){return Object(a["a"])({url:"/asset/info/"+e,method:"delete"})}},fcb7:function(e,t,n){"use strict";n.d(t,"f",(function(){return r})),n.d(t,"d",(function(){return s})),n.d(t,"e",(function(){return i})),n.d(t,"c",(function(){return l})),n.d(t,"a",(function(){return o})),n.d(t,"g",(function(){return u})),n.d(t,"b",(function(){return c}));var a=n("b775");function r(e){return Object(a["a"])({url:"/system/dept/option",method:"get",params:e})}function s(e){return Object(a["a"])({url:"/system/dept/list",method:"get",params:e})}function i(e){return Object(a["a"])({url:"/system/dept/list/exclude/"+e,method:"get"})}function l(e){return Object(a["a"])({url:"/system/dept/"+e,method:"get"})}function o(e){return Object(a["a"])({url:"/system/dept",method:"post",data:e})}function u(e){return Object(a["a"])({url:"/system/dept",method:"put",data:e})}function c(e){return Object(a["a"])({url:"/system/dept/"+e,method:"delete"})}}}]);