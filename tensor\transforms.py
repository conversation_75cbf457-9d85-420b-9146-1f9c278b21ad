import random
import torch
import torchvision.transforms as T
import torchvision.transforms.functional as F

class Compose:
    def __init__(self, transforms):
        self.transforms = transforms

    def __call__(self, image, target):
        for t in self.transforms:
            image, target = t(image, target)
        return image, target

class ToTensor:
    def __call__(self, image, target):
        image = F.to_tensor(image)
        return image, target

class RandomHorizontalFlip:
    def __init__(self, prob=0.5):
        self.prob = prob

    def __call__(self, image, target):
        if random.random() < self.prob:
            height, width = image.shape[-2:]
            image = F.hflip(image)
            if "boxes" in target:
                boxes = target["boxes"]
                boxes[:, [0, 2]] = width - boxes[:, [2, 0]]
                target["boxes"] = boxes
        return image, target

class RandomIoUCrop:
    def __init__(self, min_scale=0.3, max_scale=1.0):
        self.min_scale = min_scale
        self.max_scale = max_scale
        
    def __call__(self, image, target):
        w, h = F._get_image_size(image)
        
        for _ in range(10):
            scale = random.uniform(self.min_scale, self.max_scale)
            min_ar = 0.5
            max_ar = 2
            aspect_ratio = random.uniform(min_ar, max_ar)
            
            h_crop = int(scale * h)
            w_crop = int(aspect_ratio * h_crop)
            
            if w_crop > w or h_crop > h:
                continue
                
            i = random.randint(0, h - h_crop)
            j = random.randint(0, w - w_crop)
            
            # 裁剪图像
            image = F.crop(image, i, j, h_crop, w_crop)
            
            # 更新边界框
            if "boxes" in target:
                boxes = target["boxes"]
                boxes[:, [0, 2]] = boxes[:, [0, 2]] - j
                boxes[:, [1, 3]] = boxes[:, [1, 3]] - i
                
                # 保持边界框在新图像范围内
                boxes[:, [0, 2]] = boxes[:, [0, 2]].clamp(0, w_crop)
                boxes[:, [1, 3]] = boxes[:, [1, 3]].clamp(0, h_crop)
                
                # 移除无效的边界框
                keep = (boxes[:, 3] > boxes[:, 1]) & (boxes[:, 2] > boxes[:, 0])
                boxes = boxes[keep]
                target["boxes"] = boxes
                if "labels" in target:
                    target["labels"] = target["labels"][keep]
                    
            return image, target
            
        return image, target

class RandomZoomOut:
    def __init__(self, fill=0):
        self.fill = fill
        
    def __call__(self, image, target):
        if random.random() < 0.5:
            return image, target
            
        height, width = F._get_image_size(image)
        scale = random.uniform(1, 2)
        h_new = int(height * scale)
        w_new = int(width * scale)
        
        # 创建新图像
        image_new = torch.full((3, h_new, w_new), self.fill, dtype=torch.float32)
        
        # 随机放置原图
        i = random.randint(0, h_new - height)
        j = random.randint(0, w_new - width)
        
        image_new[:, i:i+height, j:j+width] = image
        
        # 更新边界框
        if "boxes" in target:
            boxes = target["boxes"]
            boxes[:, [0, 2]] = boxes[:, [0, 2]] + j
            boxes[:, [1, 3]] = boxes[:, [1, 3]] + i
            target["boxes"] = boxes
            
        return image_new, target

class RandomPhotometricDistort:
    def __init__(self):
        self.pd = [
            T.ColorJitter(brightness=0.2),
            T.ColorJitter(contrast=0.2),
            T.ColorJitter(saturation=0.2),
            T.ColorJitter(hue=0.1)
        ]
        
    def __call__(self, image, target):
        if random.random() < 0.5:
            distort = random.choice(self.pd)
            image = distort(image)
        return image, target 