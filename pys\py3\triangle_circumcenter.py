import tkinter as tk
from tkinter import messagebox
import sys

def circumcenter(A, B, C):
    # 计算三角形ABC的外心坐标
    D = 2 * (A[0]*(B[1]-C[1]) + B[0]*(C[1]-A[1]) + C[0]*(A[1]-B[1]))
    Ux = ((A[0]**2 + A[1]**2)*(B[1]-C[1]) + 
          (B[0]**2 + B[1]**2)*(C[1]-A[1]) + 
          (C[0]**2 + C[1]**2)*(A[1]-B[1])) / D
    Uy = ((A[0]**2 + A[1]**2)*(C[0]-B[0]) + 
          (B[0]**2 + B[1]**2)*(A[0]-C[0]) + 
          (C[0]**2 + C[1]**2)*(B[0]-A[0])) / D
    return (Ux, Uy)

class CircumcenterApp:
    def __init__(self, root):
        self.root = root
        self.root.title("三角形外心计算器")
        self.root.geometry("400x300")  # 设置窗口大小
        self.root.resizable(False, False)  # 禁止调整窗口大小
        
        # 创建主框架
        main_frame = tk.Frame(root, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建输入框和标签
        input_frame = tk.Frame(main_frame)
        input_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(input_frame, text="点A (x,y):", width=10).grid(row=0, column=0, padx=5, pady=5)
        self.entry_A = tk.Entry(input_frame, width=20)
        self.entry_A.grid(row=0, column=1, padx=5, pady=5)
        
        tk.Label(input_frame, text="点B (x,y):", width=10).grid(row=1, column=0, padx=5, pady=5)
        self.entry_B = tk.Entry(input_frame, width=20)
        self.entry_B.grid(row=1, column=1, padx=5, pady=5)
        
        tk.Label(input_frame, text="点C (x,y):", width=10).grid(row=2, column=0, padx=5, pady=5)
        self.entry_C = tk.Entry(input_frame, width=20)
        self.entry_C.grid(row=2, column=1, padx=5, pady=5)
        
        # 创建计算按钮
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        self.calc_button = tk.Button(button_frame, text="计算外心", width=15, command=self.calculate)
        self.calc_button.pack()
        
        # 创建结果显示区域
        result_frame = tk.Frame(main_frame, relief=tk.SUNKEN, borderwidth=1)
        result_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.result_label = tk.Label(result_frame, text="外心坐标将显示在这里", font=("Arial", 12))
        self.result_label.pack(pady=20)
    
    def calculate(self):
        try:
            # 获取输入并转换为坐标
            A = tuple(map(float, self.entry_A.get().split(',')))
            B = tuple(map(float, self.entry_B.get().split(',')))
            C = tuple(map(float, self.entry_C.get().split(',')))
            
            # 计算外心
            center = circumcenter(A, B, C)
            
            # 显示结果
            self.result_label.config(text=f"外心坐标: ({center[0]:.2f}, {center[1]:.2f})")
        except Exception as e:
            messagebox.showerror("错误", "请输入有效的坐标格式，例如：1.0,2.0")

if __name__ == "__main__":
    # 确保程序以GUI模式运行
    if sys.platform == "win32":
        import ctypes
        ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)
    
    root = tk.Tk()
    app = CircumcenterApp(root)
    root.mainloop()