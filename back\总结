

~~~~~~~~~~~~~~~~

好的，我将整理一个完整的Markdown文档，描述如何在CentOS 7上设置SpringBoot应用的自启动服务。

```markdown:springboot-systemd-setup.md
# SpringBoot应用设置为CentOS 7系统服务

本文档描述如何将SpringBoot应用配置为CentOS 7的系统服务，实现开机自启动功能。

## 1. 服务配置文件

创建systemd服务配置文件 `/etc/systemd/system/cdaid.service`：

```bash
[Unit]
Description=CDAID Spring Boot Application
# 在网络和系统基础服务启动后启动
After=network.target network-online.target syslog.target
# 添加其他依赖的服务，比如数据库等
After=mysql.service redis.service
# 确保网络已经准备就绪
Wants=network-online.target

[Service]
Type=forking
# 修改为实际的工作目录
WorkingDirectory=/home/<USER>/pro/back
# 使用 root 用户运行，因为当前是用 root 执行
User=root
# 设置完整的环境变量
Environment="JAVA_HOME=/home/<USER>/tools/jdk/jdk18"
Environment="PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/tools/jdk/jdk18/bin"
# 添加标准输出和错误输出的日志
StandardOutput=append:/var/log/cdaid.log
StandardError=append:/var/log/cdaid.error.log
# 使用完整路径执行命令
ExecStart=/bin/sh /home/<USER>/pro/back/s.sh start
# 停止命令
ExecStop=/bin/sh /home/<USER>/pro/back/s.sh stop
# 重启命令
ExecRestart=/bin/sh /home/<USER>/pro/back/s.sh restart
# 添加重启策略
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## 2. 启动脚本

创建启动脚本 `/home/<USER>/pro/back/s.sh`：

```bash
#!/bin/sh
# ./ry.sh start 启动 stop 停止 restart 重启 status 状态
AppName=cdaid.jar
export JAVA_HOME=/home/<USER>/tools/jdk/jdk18
export PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:$JAVA_HOME/bin
# JVM参数
JVM_OPTS="-Dname=$AppName  -Duser.timezone=Asia/Shanghai -Xms1024m -Xmx2048m -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -XX:+PrintGCDateStamps  -XX:+PrintGCDetails -XX:NewRatio=1 -XX:SurvivorRatio=30 -XX:+UseParallelGC -XX:+UseParallelOldGC"
APP_HOME=/home/<USER>/pro/back
LOG_PATH=$APP_HOME/logs/$AppName.log

if [ "$1" = "" ];
then
    echo -e "\033[0;31m 未输入操作名 \033[0m  \033[0;34m {start|stop|restart|status} \033[0m"
    exit 1
fi

if [ "$AppName" = "" ];
then
    echo -e "\033[0;31m 未输入应用名 \033[0m"
    exit 1
fi

function start()
{
    PID=$(/usr/bin/ps -ef | /usr/bin/grep java | /usr/bin/grep $AppName | /usr/bin/grep -v grep | /usr/bin/awk '{print $2}')

    if [ x"$PID" != x"" ]; then
        echo "$AppName is running..."
    else
        echo "Starting $AppName..."
        /usr/bin/mkdir -p $APP_HOME/logs
        nohup $JAVA_HOME/bin/java $JVM_OPTS -jar $APP_HOME/$AppName > $LOG_PATH 2>&1 &
        echo "Start $AppName success..."
    fi
}

function stop()
{
    echo "Stop $AppName"

    PID=""
    query(){
        PID=$(/usr/bin/ps -ef | /usr/bin/grep java | /usr/bin/grep $AppName | /usr/bin/grep -v grep | /usr/bin/awk '{print $2}')
    }

    query
    if [ x"$PID" != x"" ]; then
        echo "Stopping $AppName (pid:$PID)..."
        /bin/kill -TERM $PID
        echo "$AppName (pid:$PID) exiting..."
        while [ x"$PID" != x"" ]
        do
            sleep 1
            query
        done
        echo "$AppName exited."
    else
        echo "$AppName already stopped."
    fi
}

function restart()
{
    stop
    sleep 2
    start
}

function status()
{
    PID=`ps -ef |grep java|grep $AppName|grep -v grep|wc -l`
    if [ $PID != 0 ];then
        echo "$AppName is running..."
    else
        echo "$AppName is not running..."
    fi
}

case $1 in
    start)
    start;;
    stop)
    stop;;
    restart)
    restart;;
    status)
    status;;
    *)
    echo -e "\033[0;31m 未知操作 \033[0m  \033[0;34m {start|stop|restart|status} \033[0m"
    exit 1
esac
```

## 3. 设置权限和创建目录

执行以下命令设置必要的权限和创建目录：

```bash
# 确保脚本有执行权限
chmod +x /home/<USER>/pro/back/s.sh

# 确保jar文件有执行权限
chmod 755 /home/<USER>/pro/back/cdaid.jar

# 创建日志目录
mkdir -p /home/<USER>/pro/back/logs
chmod 755 /home/<USER>/pro/back/logs

# 创建系统日志文件
touch /var/log/cdaid.log /var/log/cdaid.error.log
chmod 666 /var/log/cdaid.log /var/log/cdaid.error.log
```

## 4. 启用服务

执行以下命令启用并启动服务：

```bash
# 重新加载systemd配置
systemctl daemon-reload

# 设置开机自启
systemctl enable cdaid.service

# 启动服务
systemctl start cdaid.service

# 查看服务状态
systemctl status cdaid.service
```

## 5. 服务管理命令

常用的服务管理命令：

```bash
# 启动服务
systemctl start cdaid.service

# 停止服务
systemctl stop cdaid.service

# 重启服务
systemctl restart cdaid.service

# 查看服务状态
systemctl status cdaid.service

# 查看服务日志
journalctl -u cdaid.service -n 50 --no-pager
```

## 注意事项

1. 确保所有路径都使用绝对路径
2. 确保JAVA_HOME和PATH环境变量正确设置
3. 确保所有系统命令使用完整路径
4. 确保目录和文件权限正确
5. 建议在其他依赖服务（如数据库）之后启动
```

==================
