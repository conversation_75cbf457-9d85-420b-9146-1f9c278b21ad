# MQTT 内外网桥接项目

这个项目实现了一个MQTT消息桥接系统，用于在内部网络和外部网络之间转发MQTT消息。

## 功能特点

- 支持内外网MQTT代理之间的双向消息转发
- 提供简单的发布者和订阅者测试程序
- 使用Eclipse Paho MQTT客户端库
- 支持可配置的主题过滤
- 包含完整的日志记录

## 系统要求

- Java 11或更高版本
- Maven 3.6或更高版本
- 内部网络MQTT代理（如Mosquitto）
- 外部网络MQTT代理

## 快速开始

1. 克隆项目并编译：
```bash
git clone [项目地址]
cd mqtt-bridge
mvn clean package
```

2. 启动MQTT桥接器：
```bash
java -cp target/mqtt-bridge-1.0-SNAPSHOT.jar com.example.mqtt.MqttBridge
```

3. 启动测试订阅者（可选）：
```bash
java -cp target/mqtt-bridge-1.0-SNAPSHOT.jar com.example.mqtt.MqttSubscriber
```

4. 启动测试发布者（可选）：
```bash
java -cp target/mqtt-bridge-1.0-SNAPSHOT.jar com.example.mqtt.MqttPublisher
```

## 配置说明

在运行程序之前，请根据实际环境修改以下配置：

1. 内部MQTT代理地址（默认：tcp://localhost:1883）
2. 外部MQTT代理地址（默认：tcp://test.mosquitto.org:1883）
3. 需要转发的主题（默认：bridge/test/#）

这些配置可以在各个类的main方法中修改。

## 使用示例

1. 启动MQTT桥接器后，它会自动连接到内部和外部MQTT代理。
2. 启动订阅者程序，它会监听指定主题上的所有消息。
3. 使用发布者程序发送测试消息。
4. 观察消息如何通过桥接器在内外网之间转发。

## 注意事项

1. 确保MQTT代理已正确配置并运行。
2. 检查防火墙设置，确保MQTT端口（默认1883）已开放。
3. 在生产环境中使用时，建议配置适当的安全设置（如TLS、用户名密码等）。

## 问题排查

如果遇到连接问题：

1. 检查MQTT代理是否正在运行
2. 验证代理地址和端口是否正确
3. 检查网络连接是否正常
4. 查看程序日志获取详细错误信息

## 贡献指南

欢迎提交问题报告和改进建议。如果要贡献代码：

1. Fork 项目
2. 创建特性分支
3. 提交变更
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License 