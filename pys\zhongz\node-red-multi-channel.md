# Node-RED 多通道消息队列配置

1. **发送方 (A) 的流程**

   a. 配置 `http in` 节点：
   - Method: POST
   - URL: /api/send/:channel
   - Name: Send Data

   b. 修改 `function` 节点代码：
   ```javascript
   // 获取频道信息
   const channel = msg.req.params.channel;
   
   // 存储消息到全局上下文
   let messages = global.get("messages") || {};
   if (!messages[channel]) {
       messages[channel] = [];
   }
   
   // 构建消息对象
   const messageObj = {
       id: Date.now(),
       channel: channel,
       data: msg.payload,
       processed: false,
       timestamp: new Date().toISOString()
   };
   
   // 存储消息
   messages[channel].push(messageObj);
   global.set("messages", messages);
   
   // 返回成功消息
   msg.payload = {
       status: "success",
       messageId: messageObj.id,
       channel: channel
   };
   return msg;
   ```

2. **接收方 (B) 的流程**

   a. 配置 `http in` 节点：
   - Method: GET
   - URL: /api/receive/:channel
   - Name: Receive Data

   b. 修改接收方的 `function` 节点代码：
   ```javascript
   // 获取频道信息
   const channel = msg.req.params.channel;
   
   // 获取消息列表
   let messages = global.get("messages") || {};
   let channelMessages = messages[channel] || [];
   
   // 查找第一条未处理的消息
   let unprocessed = channelMessages.find(m => !m.processed);
   
   if (unprocessed) {
       // 标记为已处理
       unprocessed.processed = true;
       global.set("messages", messages);
       
       msg.payload = {
           status: "success",
           messageId: unprocessed.id,
           channel: channel,
           data: unprocessed.data,
           timestamp: unprocessed.timestamp
       };
   } else {
       msg.payload = {
           status: "empty",
           channel: channel,
           message: "No new messages in this channel"
       };
   }
   
   return msg;
   ```

3. **测试命令**

发送数据到不同频道：
```bash
# 发送到 channel1
curl -X POST http://你的服务器IP:1880/api/send/channel1 \
     -H "Content-Type: application/json" \
     -d '{"message": "Message for channel 1"}'

# 发送到 channel2
curl -X POST http://你的服务器IP:1880/api/send/channel2 \
     -H "Content-Type: application/json" \
     -d '{"message": "Message for channel 2"}'
```

从不同频道接收数据：
```bash
# 从 channel1 接收
curl http://你的服务器IP:1880/api/receive/channel1

# 从 channel2 接收
curl http://你的服务器IP:1880/api/receive/channel2
```

特点：
1. 支持多个独立的频道
2. 每个消息只能被获取一次
3. 消息带有时间戳和唯一ID
4. 不同频道的消息互不影响 