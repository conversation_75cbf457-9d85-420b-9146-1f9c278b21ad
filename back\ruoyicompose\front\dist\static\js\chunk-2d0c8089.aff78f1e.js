(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c8089"],{"52c9":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"告警类型",prop:"type"}},[a("el-select",{attrs:{placeholder:"请选择告警类型",clearable:""},on:{change:e.handleQuery,clear:e.handleQuery},model:{value:e.queryParams.type,callback:function(t){e.$set(e.queryParams,"type",t)},expression:"queryParams.type"}},e._l(e.dict.type.asset_alarm_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:alarm:export"],expression:"['asset:alarm:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.alarmList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"告警类型",align:"center",prop:"type"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.asset_alarm_type,value:t.row.type}})]}}])}),a("el-table-column",{attrs:{label:"区库",align:"center",prop:"deptName"}}),a("el-table-column",{attrs:{label:"物资",align:"center",prop:"assetName"}}),a("el-table-column",{attrs:{label:"物资条码",align:"center",prop:"epc"}}),a("el-table-column",{attrs:{label:"告警日期",align:"center",prop:"alarmDate"}}),a("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"告警类型",prop:"type"}},[a("el-select",{attrs:{placeholder:"请选择告警类型"},model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},e._l(e.dict.type.asset_alarm_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"物资",prop:"assetId"}},[a("el-select",{attrs:{placeholder:"请选择物资"},model:{value:e.form.assetId,callback:function(t){e.$set(e.form,"assetId",t)},expression:"form.assetId"}},e._l(e.dict.type.asset_alarm_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:parseInt(e.value)}})})),1)],1),a("el-form-item",{attrs:{label:"物资明细",prop:"assetItemId"}},[a("el-select",{attrs:{placeholder:"请选择物资明细"},model:{value:e.form.assetItemId,callback:function(t){e.$set(e.form,"assetItemId",t)},expression:"form.assetItemId"}},e._l(e.dict.type.asset_alarm_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:parseInt(e.value)}})})),1)],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{placeholder:"请输入备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},r=[],s=a("5530"),n=(a("d81d"),a("b775"));function o(e){return Object(n["a"])({url:"/asset/alarm/list",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/asset/alarm/"+e,method:"get"})}function u(e){return Object(n["a"])({url:"/asset/alarm",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/asset/alarm",method:"put",data:e})}function m(e){return Object(n["a"])({url:"/asset/alarm/"+e,method:"delete"})}var p={name:"Alarm",dicts:["asset_alarm_type"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,alarmList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,type:null,assetId:null,assetItemId:null,createTime:null},form:{},rules:{type:[{required:!0,message:"告警类型不能为空",trigger:"change"}],assetId:[{required:!0,message:"物资不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.alarmList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,type:null,assetId:null,assetItemId:null,createTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加告警管理"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;i(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改告警管理"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?c(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除告警管理编号为"'+a+'"的数据项？').then((function(){return m(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("asset/alarm/export",Object(s["a"])({},this.queryParams),"alarm_".concat((new Date).getTime(),".xlsx"))}}},d=p,h=a("2877"),f=Object(h["a"])(d,l,r,!1,null,null,null);t["default"]=f.exports}}]);