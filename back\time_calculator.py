import tkinter as tk
from tkinter import messagebox
from datetime import datetime, timedelta

def validate_numeric_input(P):
    if <PERSON><PERSON>isdigit() or P == "":
        return True
    else:
        return False

def calculate_time():
    try:
        # 获取输入值并设置默认值
        hours = int(hours_entry.get() or 0)
        minutes = int(minutes_entry.get() or 0)
        seconds = int(seconds_entry.get() or 0)
        
        # 输入验证
        if hours < 0 or minutes < 0 or seconds < 0:
            raise ValueError("请输入非负整数")
            
        if minutes >= 60 or seconds >= 60:
            raise ValueError("分钟/秒数不能超过59")
        
        current_time = datetime.now()
        delta = timedelta(
            hours=hours,
            minutes=minutes,
            seconds=seconds
        )
        result_time = current_time + delta
        
        result_label.config(
            text=f"计算结果：\n{result_time.strftime('%Y-%m-%d %H:%M:%S')}",
            fg="green"
        )
    except ValueError as e:
        messagebox.showerror("输入错误", str(e))
        result_label.config(text="请输入有效的数字", fg="red")
    except Exception as e:
        messagebox.showerror("错误", str(e))

# 创建主窗口
root = tk.Tk()
root.title("时间计算器")
root.geometry("400x250")

# 使用Grid布局
input_frame = tk.Frame(root)
input_frame.pack(pady=10)

# 验证函数注册
validate_command = root.register(validate_numeric_input)

# 小时输入
tk.Label(input_frame, text="小时:").grid(row=0, column=0, padx=5)
hours_entry = tk.Entry(input_frame, width=8, validate="key", validatecommand=(validate_command, '%P'))
hours_entry.grid(row=0, column=1, padx=5)

# 分钟输入
tk.Label(input_frame, text="分钟:").grid(row=0, column=2, padx=5)
minutes_entry = tk.Entry(input_frame, width=8, validate="key", validatecommand=(validate_command, '%P'))
minutes_entry.grid(row=0, column=3, padx=5)

# 秒输入
tk.Label(input_frame, text="秒:").grid(row=0, column=4, padx=5)
seconds_entry = tk.Entry(input_frame, width=8, validate="key", validatecommand=(validate_command, '%P'))
seconds_entry.grid(row=0, column=5, padx=5)

# 计算按钮
tk.Button(root, text="计算", command=calculate_time,
         bg="#4CAF50", fg="white", width=15).pack(pady=10)

# 结果显示
result_label = tk.Label(root, text="",
                       font=("Arial", 12),
                       height=3)
result_label.pack(pady=10)

# 启动主循环
root.mainloop()