@echo off
chcp 65001 >nul
echo 🚀 排序算法测试程序
echo ==================

echo.
echo 📝 编译 Java 文件...
javac -encoding UTF-8 FastSortingAlgorithms.java
if %errorlevel% neq 0 (
    echo ❌ FastSortingAlgorithms.java 编译失败
    pause
    exit /b 1
)

javac -encoding UTF-8 SortingAlgorithmTest.java
if %errorlevel% neq 0 (
    echo ❌ SortingAlgorithmTest.java 编译失败
    pause
    exit /b 1
)

echo ✅ 编译成功！

echo.
echo 🎯 运行排序算法演示...
echo ========================
java FastSortingAlgorithms

echo.
echo.
echo 🧪 运行完整测试套件...
echo =====================
java SortingAlgorithmTest

echo.
echo ✅ 所有程序运行完成！
pause
