(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7d1bde16"],{"9afd":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"区库",prop:"deptId"}},[a("el-select",{staticStyle:{width:"350px"},attrs:{placeholder:"请选择区库",clearable:"",filterable:""},on:{change:e.handleQuery,clear:e.handleQuery},model:{value:e.queryParams.deptId,callback:function(t){e.$set(e.queryParams,"deptId",t)},expression:"queryParams.deptId"}},e._l(e.deptOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.text,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"物资名称",prop:"assetName"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入物资名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.assetName,callback:function(t){e.$set(e.queryParams,"assetName",t)},expression:"queryParams.assetName"}})],1),a("el-form-item",{attrs:{label:"操作方式",prop:"type"}},[a("el-select",{attrs:{placeholder:"请选择操作方式",clearable:"",filterable:""},on:{change:e.handleQuery,clear:e.handleQuery},model:{value:e.queryParams.type,callback:function(t){e.$set(e.queryParams,"type",t)},expression:"queryParams.type"}},e._l(e.dict.type.asset_log_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"操作时间",prop:"createTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择操作时间"},model:{value:e.queryParams.createTime,callback:function(t){e.$set(e.queryParams,"createTime",t)},expression:"queryParams.createTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:log:export"],expression:"['asset:log:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.logList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"区库",align:"center",prop:"deptName"}}),a("el-table-column",{attrs:{label:"物资名称",align:"center",prop:"assetName"}}),a("el-table-column",{attrs:{label:"条码",align:"center",prop:"epc"}}),a("el-table-column",{attrs:{label:"货架",align:"center",prop:"goodsnum"}}),a("el-table-column",{attrs:{label:"库门",align:"center",prop:"createBy"}}),a("el-table-column",{attrs:{label:"操作方式",align:"center",prop:"type"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.asset_log_type,value:t.row.type}})]}}])}),a("el-table-column",{attrs:{label:"时间",align:"center",prop:"createTime"}})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"物资明细",prop:"assetItemId"}},[a("el-select",{attrs:{placeholder:"请选择物资明细"},model:{value:e.form.assetItemId,callback:function(t){e.$set(e.form,"assetItemId",t)},expression:"form.assetItemId"}},e._l(e.dict.type.asset_log_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:parseInt(e.value)}})})),1)],1),a("el-form-item",{attrs:{label:"区库",prop:"deptId"}},[a("el-select",{attrs:{placeholder:"请选择区库"},model:{value:e.form.deptId,callback:function(t){e.$set(e.form,"deptId",t)},expression:"form.deptId"}},e._l(e.dict.type.asset_log_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:parseInt(e.value)}})})),1)],1),a("el-form-item",{attrs:{label:"操作方式",prop:"type"}},[a("el-select",{attrs:{placeholder:"请选择操作方式"},model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},e._l(e.dict.type.asset_log_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{placeholder:"请输入备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],n=a("5530"),s=(a("d81d"),a("b775"));function o(e){return Object(s["a"])({url:"/asset/log/list",method:"get",params:e})}function i(e){return Object(s["a"])({url:"/asset/log/"+e,method:"get"})}function u(e){return Object(s["a"])({url:"/asset/log",method:"post",data:e})}function c(e){return Object(s["a"])({url:"/asset/log",method:"put",data:e})}function p(e){return Object(s["a"])({url:"/asset/log/"+e,method:"delete"})}var d=a("fcb7"),m={name:"Log",dicts:["asset_log_type"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,logList:[],deptOptions:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,assetItemId:null,deptId:null,type:null,createTime:null,assetName:null},form:{},rules:{assetItemId:[{required:!0,message:"物资明细不能为空",trigger:"change"}],deptId:[{required:!0,message:"区库不能为空",trigger:"change"}],type:[{required:!0,message:"操作方式不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.logList=t.rows,e.total=t.total,e.loading=!1})),this.getDeptOption()},getDeptOption:function(){var e=this;Object(d["f"])({type:"1"}).then((function(t){e.deptOptions=t.data}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,assetItemId:null,deptId:null,type:null,createBy:null,createTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加出入库"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;i(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改出入库"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?c(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除出入库编号为"'+a+'"的数据项？').then((function(){return p(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("asset/log/export",Object(n["a"])({},this.queryParams),"log_".concat((new Date).getTime(),".xlsx"))}}},f=m,h=a("2877"),g=Object(h["a"])(f,r,l,!1,null,null,null);t["default"]=g.exports},fcb7:function(e,t,a){"use strict";a.d(t,"f",(function(){return l})),a.d(t,"d",(function(){return n})),a.d(t,"e",(function(){return s})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return i})),a.d(t,"g",(function(){return u})),a.d(t,"b",(function(){return c}));var r=a("b775");function l(e){return Object(r["a"])({url:"/system/dept/option",method:"get",params:e})}function n(e){return Object(r["a"])({url:"/system/dept/list",method:"get",params:e})}function s(e){return Object(r["a"])({url:"/system/dept/list/exclude/"+e,method:"get"})}function o(e){return Object(r["a"])({url:"/system/dept/"+e,method:"get"})}function i(e){return Object(r["a"])({url:"/system/dept",method:"post",data:e})}function u(e){return Object(r["a"])({url:"/system/dept",method:"put",data:e})}function c(e){return Object(r["a"])({url:"/system/dept/"+e,method:"delete"})}}}]);