package com.example.mqtt;

import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MqttPublisher {
    private static final Logger logger = LoggerFactory.getLogger(MqttPublisher.class);
    
    private final MqttClient client;
    private final String topic;
    
    public MqttPublisher(String brokerUrl, String clientId, String topic, String username, String password) throws MqttException {
        this.client = new MqttClient(brokerUrl, clientId, new MemoryPersistence());
        this.topic = topic;
        
        MqttConnectOptions options = new MqttConnectOptions();
        options.setCleanSession(true);
        options.setUserName(username);
        options.setPassword(password.toCharArray());
        options.setAutomaticReconnect(true);
        options.setConnectionTimeout(10);
        client.connect(options);
        logger.info("Connected to broker: {}", brokerUrl);
    }
    
    public void publish(String message) throws MqttException {
        MqttMessage mqttMessage = new MqttMessage(message.getBytes());
        client.publish(topic, mqttMessage);
        logger.info("Published message: {}", message);
    }
    
    public void disconnect() {
        try {
            if (client != null && client.isConnected()) {
                client.disconnect();
                client.close();
            }
            logger.info("Disconnected from broker");
        } catch (MqttException e) {
            logger.error("Error disconnecting from broker", e);
        }
    }
    
    public static void main(String[] args) {
        String broker = "tcp://localhost:1883";  // MQTT代理地址
        String clientId = "mqtt-publisher";
        String topic = "bridge/test/message";
        String username = "admin";  // MQTT用户名
        String password = "msgbus!QAZ@wsx";  // MQTT密码
        
        try {
            MqttPublisher publisher = new MqttPublisher(broker, clientId, topic, username, password);
            
            // 发送测试消息
            for (int i = 1; i <= 5; i++) {
                String message = "Test message " + i;
                publisher.publish(message);
                Thread.sleep(1000);
            }
            
            publisher.disconnect();
        } catch (Exception e) {
            logger.error("Error running publisher", e);
        }
    }
} 