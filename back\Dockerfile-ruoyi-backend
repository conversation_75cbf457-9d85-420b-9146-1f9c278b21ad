# 使用 OpenJDK 18 作为基础镜像
FROM openjdk:18-jdk-alpine

# 设置工作目录
WORKDIR /app

# 将 Maven 构建的 JAR 文件复制到容器中
COPY back/cdaid.jar app.jar

# 设置环境变量
ENV JAVA_OPTS="-Dname=cdaid.jar -Dfile.encoding=UTF-8 -Duser.timezone=Asia/Shanghai -Xms1024m -Xmx2048m -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -XX:+PrintGCDateStamps -XX:+PrintGCDetails -XX:NewRatio=1 -XX:SurvivorRatio=30 -XX:+UseParallelGC -XX:+UseParallelOldGC"

# 暴露端口
EXPOSE 8899

# 定义启动命令
CMD ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
