package com.example.mqtt;

import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MqttBridge {
    private static final Logger logger = LoggerFactory.getLogger(MqttBridge.class);
    
    private final String internalBrokerUrl;
    private final String externalBrokerUrl;
    private final String clientId;
    private final String topic;
    private final String username;
    private final String password;
    
    private MqttClient internalClient;
    private MqttClient externalClient;
    
    public MqttBridge(String internalBrokerUrl, String externalBrokerUrl, String clientId, 
                      String topic, String username, String password) {
        this.internalBrokerUrl = internalBrokerUrl;
        this.externalBrokerUrl = externalBrokerUrl;
        this.clientId = clientId;
        this.topic = topic;
        this.username = username;
        this.password = password;
    }
    
    public void start() throws MqttException {
        // 连接内部MQTT代理
        internalClient = new MqttClient(internalBrokerUrl, clientId + "-internal", new MemoryPersistence());
        MqttConnectOptions internalOptions = new MqttConnectOptions();
        internalOptions.setCleanSession(true);
        internalOptions.setUserName(username);
        internalOptions.setPassword(password.toCharArray());
        internalOptions.setAutomaticReconnect(true);
        internalOptions.setConnectionTimeout(10);
        internalClient.connect(internalOptions);
        logger.info("Connected to internal broker: {}", internalBrokerUrl);
        
        // 连接外部MQTT代理
        externalClient = new MqttClient(externalBrokerUrl, clientId + "-external", new MemoryPersistence());
        MqttConnectOptions externalOptions = new MqttConnectOptions();
        externalOptions.setCleanSession(true);
        externalOptions.setUserName(username);
        externalOptions.setPassword(password.toCharArray());
        externalOptions.setAutomaticReconnect(true);
        externalOptions.setConnectionTimeout(10);
        externalClient.connect(externalOptions);
        logger.info("Connected to external broker: {}", externalBrokerUrl);
        
        // 设置消息转发
        setupMessageForwarding();
    }
    
    private void setupMessageForwarding() throws MqttException {
        // 从内部到外部的转发
        internalClient.subscribe(topic, (topic, message) -> {
            try {
                externalClient.publish(topic, message);
                logger.info("Forwarded message from internal to external: {}", new String(message.getPayload()));
            } catch (MqttException e) {
                logger.error("Error forwarding message to external broker", e);
            }
        });
        
        // 从外部到内部的转发
        externalClient.subscribe(topic, (topic, message) -> {
            try {
                internalClient.publish(topic, message);
                logger.info("Forwarded message from external to internal: {}", new String(message.getPayload()));
            } catch (MqttException e) {
                logger.error("Error forwarding message to internal broker", e);
            }
        });
    }
    
    public void stop() {
        try {
            if (internalClient != null && internalClient.isConnected()) {
                internalClient.disconnect();
                internalClient.close();
            }
            if (externalClient != null && externalClient.isConnected()) {
                externalClient.disconnect();
                externalClient.close();
            }
            logger.info("MQTT bridge stopped");
        } catch (MqttException e) {
            logger.error("Error stopping MQTT bridge", e);
        }
    }
    
    public static void main(String[] args) {
        String internalBroker = "tcp://localhost:1883";  // 内部MQTT代理地址
        String externalBroker = "tcp://test.mosquitto.org:1883";  // 外部MQTT代理地址
        String clientId = "mqtt-bridge";
        String topic = "bridge/test/#";  // 要转发的主题
        String username = "admin";  // MQTT用户名
        String password = "msgbus!QAZ@wsx";  // MQTT密码
        
        try {
            MqttBridge bridge = new MqttBridge(internalBroker, externalBroker, clientId, topic, username, password);
            bridge.start();
            
            // 添加关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(bridge::stop));
            
            logger.info("MQTT bridge is running. Press Ctrl+C to stop.");
            
            // 保持程序运行
            Thread.currentThread().join();
        } catch (Exception e) {
            logger.error("Error running MQTT bridge", e);
        }
    }
} 