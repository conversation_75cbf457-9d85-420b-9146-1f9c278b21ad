

~~~~~~~~~~~~~~~~
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简历</title>
    <style>
        body {
            font-family: Sim<PERSON><PERSON>, <PERSON>l, sans-serif;
            margin: 0;
            padding: 0;
            color: #000;
        }
        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            font-size: 24px;
            margin: 20px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #000;
            padding: 5px 8px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #ccc;
            font-weight: bold;
        }
        .section-header {
            background-color: #ccc;
            font-weight: bold;
            text-align: left;
        }
        .photo {
            width: 120px;
            height: 160px;
        }
        .bullet {
            margin-left: 20px;
            position: relative;
        }
        .bullet:before {
            content: "■";
            position: absolute;
            left: -20px;
        }
        .page-number {
            text-align: left;
            margin-top: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>简历</h1>
        
        <table>
            <tr>
                <th colspan="2">基本信息</th>
                <td rowspan="7" style="width: 120px; text-align: center;">
                    <!-- 照片位置 -->
                    <img class="photo" src="照片位置" alt="个人照片">
                </td>
            </tr>
            <tr>
                <td>姓　　名</td>
                <td>常曹</td>
            </tr>
            <tr>
                <td>民　　族</td>
                <td>汉</td>
            </tr>
            <tr>
                <td>电　　话</td>
                <td>13711136253</td>
            </tr>
            <tr>
                <td>邮　　箱</td>
                <td><EMAIL></td>
            </tr>
            <tr>
                <td>住　　址</td>
                <td>江苏无锡新区</td>
            </tr>
            <tr>
                <td>求职意向</td>
                <td>部门主管、系统架构</td>
            </tr>
            <tr>
                <td>出生年月</td>
                <td>19830310</td>
            </tr>
            <tr>
                <td>身　　高</td>
                <td>170CM</td>
            </tr>
            <tr>
                <td>政治面貌</td>
                <td>白身</td>
            </tr>
            <tr>
                <td>毕业院校</td>
                <td>常州大学</td>
            </tr>
            <tr>
                <td>学　　历</td>
                <td>本科</td>
            </tr>
            <tr>
                <td>入职时间</td>
                <td>一个月内</td>
            </tr>
        </table>

        <table>
            <tr>
                <td class="section-header">教育背景</td>
            </tr>
            <tr>
                <td>2004.09-2007.06</td>
                <td>常州大学学院</td>
                <td>计算机科学与技术（本科）</td>
            </tr>
        </table>

        <table>
            <tr>
                <td class="section-header">工作经验</td>
            </tr>
            <tr>
                <td>2018.01—至今</td>
                <td>无锡萨非特智能科技有限公司</td>
                <td>研发部门主管</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p class="bullet">部门管理，技术牵头。</p>
                    <p class="bullet">物联网产品规划，设计，软件架构。</p>
                    <p class="bullet">项目需求分析，管理开发跟踪。</p>
                    <p class="bullet">外部项目沟通，资源利用。</p>
                </td>
            </tr>
            <tr>
                <td>2014.05—2018.11</td>
                <td>金蝶集团</td>
                <td>项目管理技术架构</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p class="bullet">负责软件需求调研、项目任务书、软件需求规格说明书的编写及确认。</p>
                    <p class="bullet">技术框架选型，原型设计讨论确认，技术疑问攻关。</p>
                    <p class="bullet">负责软件项目整体实施的计划的编制，核实，确认和发布，跟踪与监控软件项目的实施进度，确保项目按计划执行。</p>
                    <p class="bullet">完成整个项目软件部分以及与硬件接口部分的实施并确保项目的最终验收。</p>
                    <p class="bullet">负责项目履行过程中与用户，供应商，集成商，公司其它相关部门关于公司自主产品技术问题和管理问题的协调和沟通。</p>
                </td>
            </tr>
            <tr>
                <td>2007.09—2014.05</td>
                <td>中国太平洋保险公司外派</td>
                <td>项目管理技术开发</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p class="bullet">项目管理</p>
                    <p class="bullet">团队管理</p>
                    <p class="bullet">软件开发</p>
                </td>
            </tr>
        </table>

        <div class="page-number">1/5</div>

        <table>
            <tr>
                <td class="section-header">项目经验</td>
            </tr>
            <tr>
                <td>2018.11—至今</td>
                <td>企业 CMMI3 评选项目</td>
                <td>项目总负责</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>描述：</p>
                    <p>CMMI3 认证意义：企业能够对项目的实施有一整套的管理措施，并保障项目的完成；企业能够根据自身的特殊需求以及自己的标准流程，将这套管理体系与流程予以制度化，不仅能够在同类的项目上得到成功的实施，在不同类的项目上一样能够得到成功的实施，科学的管理成为企业的一种文化，企业的组织财富。</p>
                    <p>公司决定在 2019 年上半年通过 CMMI3 认证。</p>
                    <p>责任：</p>
                    <p>负责整个项目的定义工作安排，培训落实，人员组织，技术人员帮忙。</p>
                    <p>负责参评项目选型，项目文档规范构建补充。</p>
                    <p>负责参评现场的统筹安排，制定过 CMMI3 的具体流程。</p>
                </td>
            </tr>
            <tr>
                <td>2018.09—2018.11</td>
                <td>新零售无人称重货柜物联网项目（离线版）</td>
                <td>项目管理技术架构</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>描述：</p>
                    <p>无人智能货柜高线版为无人智能货柜在线版的客户定制化需求补充。</p>
                    <p>为了满足客户无电要求而定制化改造的项目，主要改造点在于结合手机 APP 二维码扫码技术以及硬件 SDK 比对对在线版本进行改造。</p>
                    <p>单片机部分实行了 2G 通信模块移除，加入二维码生成算法以及密钥生成算法。</p>
                    <p>责任：</p>
                    <p>项目管理，需求制定，进度跟踪等等。</p>
                </td>
            </tr>
            <tr>
                <td>2018.01—2018.11</td>
                <td>新零售无人称重货柜物联网项目（在线版）</td>
                <td>项目管理技术架构</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>描述：</p>
                    <p>无人智能货柜是依托物联网技术而兴起的新零售模式，而无人智能货柜平台将实现无人智能货柜线上线下智能管理，数据分析，业务监控等，实现智能化新零售。</p>
                    <p>依托物联网实现智能化购物，无人化管理。</p>
                    <p>智能终端通过各种传感器收集用户购物行为进行，如称重数据，报价预测，关门等状态收集。</p>
                    <p>移动端以小程序技术开发，用于客户选购商品，动态展示商品增减，商品支付，报警跟踪等等。</p>
                    <p>后台技术架构主要为 springboot+dubbo 实现商家管理，预售商品登记，支付设置，支付提现，信息统计等功能。</p>
                    <p>硬件通信采用 netty 框架，基于自定义的硬件协议配合密钥 SDK（jni 编译 C 语言函数）机制进行安全加密实现硬件与后台的通信。</p>
                    <p>技术栈：</p>
                    <p>Maven 管理构建项目。</p>
                    <p>thymeleaf、bootstrap、layer 等框架完成前端展示功能</p>
                    <p>MyBatis Druid 等持久层框架。</p>
                    <p>百度 webuploader、EChars 完成文件上传和图片展示</p>
                    <p>quartz 完成消费者未支付订单及提醒功能及其他系统调度任务，采用 redis 缓存技术提高系统效率</p>
                    <p>Shiro 完成数据权限信息管理。</p>
                    <p>RabbitMQ 队列技术实现发布订阅，减少远程 RPC 压力，优化性能。</p>
                    <p>支付部署采用 docker 虚拟化方式一键配置部署服务，实现灵活话部署。</p>
                    <p>责任：</p>
                </td>
            </tr>
        </table>

        <div class="page-number">2/5</div>

        <table>
            <tr>
                <td>2016.01—2016.08</td>
                <td>上图馆数据集成项目</td>
                <td>项目管理技术架构</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>描述：</p>
                    <p>上海经信委合作项目，设计上图馆现有项目之间信息孤岛打通，采用总线型机构设计。</p>
                    <p>应用金蝶 ESB 服务总线产品对上海图书馆各个系统数据实现集成，通过其提供的各种输入输出转换组件，从各种输入介质如 Text 文件、XML、Excel、DBMS 的表中获取源数据进行初始采集和增量采集，处理。</p>
                    <p>企业应用服务注册，服务编排，应用协议转换对多个服务进行编排组合，并根据服务请求方的请求内容来进行消息路由，路由至不同的服务提供方。</p>
                </td>
            </tr>
            <tr>
                <td>2013.03—2014.05</td>
                <td>上海浦东公交系统财务数据中心</td>
                <td>项目管理技术架构</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>描述：</p>
                    <p>金蝶 ESB 设计器，开发工具</p>
                    <p>前期业务梳理，数据整理。</p>
                    <p>金蝶 ESB，金蝶 MQ，sqlserver2008 软件环境打破原有上海某公交财务系统各个分部之间的数据孤立状态，成功的使其连成一线，完成数据间的逻辑 ETL 操作，促成统一网状架构。</p>
                </td>
            </tr>
            <tr>
                <td>2013.03—2014.05</td>
                <td>坐席运营系统</td>
                <td>项目管理开发</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>描述：</p>
                    <p>系统包括坐席系统部分和运营管理，如识库等部分，坐席系统包括软电话呼叫中心，通用业务，产险业务受理，产险业务查询，产险回访，寿险业务受理，寿险回访。</p>
                    <p>运营管理包含语音广播管理系统和机构的管理，品牌管理等等，回访管理等等。</p>
                    <p>技术上基于 SPING,WEBWORK,HIBERNATE,前端件基于 wps,wmq，数据库基于 ORACLE。</p>
                    <p>开发进度上，本阶段属于项目的二期，主要是完善多个运营管理系统。</p>
                </td>
            </tr>
            <tr>
                <td>2012.07—2013.02</td>
                <td>在线出单系统</td>
                <td>项目管理开发</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>描述：</p>
                    <p>在线出单系统为太平洋寿险各个分公司时的线上出单系统，拥有涵盖寿险几乎所有险种。</p>
                    <p>与诸多系统对接，实现其总打印平台功能。</p>
                    <p>数据分时时批量等方式与寿险核心同步，保单生效等。</p>
                    <p>新险种持续对接入改平台从而从分工单打印，同步入总公司集团。</p>
                </td>
            </tr>
            <tr>
                <td>2012.04—2012.07</td>
                <td>数据交换系统后台管理</td>
                <td>项目管理开发</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>描述：</p>
                    <p>为适应新平台不断增加的接口业务需要，以及旧管理系统不断升高的维护成本而重写后台管理系统。</p>
                    <p>基于 SSH 框架，前台页面显示基于 EXTJS 框架+JSON+JSP，后台数据库为 oracle，应用服务器为 was。</p>
                    <p>新系统对于产，寿险通道申请区别处理，增加通道时间维护，监视等功能，解放维护人员大量工作量。</p>
                    <p>增加相关运行，减少与业务人员沟通工作量，使得系统更具有人性化，自动化。</p>
                </td>
            </tr>
            <tr>
                <td>2011.08—2012.03</td>
                <td>WebSphere ESB 系统持续改造</td>
                <td>项目管理开发</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>描述：</p>
                    <p>WID webshpere intergration developer，ECLIPS 开发工具</p>
                    <p>AIX 硬件环境</p>
                    <p>websphere esb 软件环境基于产险 ESB 内部消息交互系统改造。</p>
                    <p>包括</p>
                    <p>1) 原有项目程序重新设计，代码重新精简，功能重新规划。</p>
                    <p>2) 监控系统进一步升级，完善。</p>
                    <p>3) 消息路由模块重构，整合。</p>
                    <p>4) 消息解析模块重构，整合。</p>
                    <p>5) 预生产环境压力评估系统开发。</p>
                </td>
            </tr>
        </table>

        <div class="page-number">3/5</div>

        <table>
            <tr>
                <td>2010.04—2011.11</td>
                <td>文件数据交互器</td>
                <td>项目管理开发</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>描述：</p>
                    <p>visual c++，vi 开发工具</p>
                    <p>HP-UX 小型机硬件环境</p>
                    <p>C 软件环境 1 为解决消息交换总线传输大文件上性能的不足的问题，开发用于点对点的文件传输适配器。</p>
                    <p>2 适配器基于 C 语言，可成功运行于 WINDOWS，LINUX，HP-UX，以及 AIX 等操作系统。</p>
                    <p>3 本模块基于 TCP-IP 下的 SOCKET 编程，采用多线程机制，线性日志模式，以及必要的备份功能。</p>
                    <p>4 重要功能则是可以确保文件分批次发送，采用 TRIG 机制。</p>
                </td>
            </tr>
            <tr>
                <td>2011.04—2011.06</td>
                <td>数据交互中心监控改造</td>
                <td>项目管理开发</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>描述：</p>
                    <p>vi 开发工具</p>
                    <p>小型机硬件环境</p>
                    <p>HP-UX，shell 脚本软件环境进一步完善关于消息交换中间件 WMQ 的相关监控系统，针对业务量大的系统，进行通道状态以及队列深度的全天监控，并且与知信系统相关接口对接，以达到发短信监控的目的。</p>
                </td>
            </tr>
            <tr>
                <td>2011.01—2011.05</td>
                <td>消息交换总线核心架构改造</td>
                <td>项目管理开发</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>描述：</p>
                    <p>visual c++，vi 开发工具</p>
                    <p>HP-UX 小型机硬件环境</p>
                    <p>C 软件环境应对新的不断增加的消息信息量，解决原有热备单工方式存在过多资源浪费，处理能比达到瓶颈等问题，现要求以中心集群方式重新部署整个 SOA 中心系统，实现 MB 双工，MQ 实现集群，前置通讯队列管理器热备的架构。</p>
                </td>
            </tr>
            <tr>
                <td>2010.03—2010.11</td>
                <td>企业级数据交互功能（PUBSUB）</td>
                <td>项目开发</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>描述：</p>
                    <p>MyEclipse，visual c++，vi 开发工具</p>
                    <p>HP-UX 小型机硬件环境</p>
                    <p>JAVA,C 软件环境 2010.3 - 2010.11</p>
                    <p>1 为适应新需求，已经完全发挥 MB 的发布订阅功能，需要开发新的适配器接口，并且改版本拥有 C,java 两种版本。</p>
                    <p>2 模块功能分别采用 JAVA 以及 C 语言编写，以适应不同的请求方或者响应方。</p>
                    <p>3 模块依赖于 websphere MB 中心代理进行主题树的生成与管理，控制队列从对于代理的控制而保持其发布订阅功能。</p>
                    <p>4 编写适配器，增加 PUBSUB 接口，实现 MQ API 连而达到能够操作中心主题树，发布主题，订阅主题。</p>
                </td>
            </tr>
            <tr>
                <td>2009.06—2010.01</td>
                <td>消息交互总线支持功能增加（TUXEDO）</td>
                <td>开发</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>描述：</p>
                    <p>vi，visual c++开发工具</p>
                    <p>HP-UX 小型机硬件环境</p>
                    <p>c 语言，HP-UX 软件环境</p>
                    <p>1 为适应消息响应方向 TUXEDO 服务，为消息交换系统增加 TUXEDO 调用客户端。</p>
                    <p>2 该模块是在 HP-UX 操作系统下基于 websphere MQ api 采用 C 语言编写。</p>
                    <p>3 具体功能负责从 MQ 队列中取走消息并调用 TUX 服务。</p>
                    <p>4 实现中采用多进程线性日志编写法。</p>
                    <p>5 后期测试以及功能改进维护等等。</p>
                </td>
            </tr>
        </table>

        <div class="page-number">4/5</div>

        <table>
            <tr>
                <td>2008.08—2009.05</td>
                <td>企业消息交换总线（IBM SOA）</td>
                <td>开发</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>描述：</p>
                    <p>MyEclipse 开发工具</p>
                    <p>HP-UX 小型机硬件环境</p>
                    <p>JAVA 软件环境 1 该项目是基于 IBM websphere message queue,IBM websphere message broker 的一套企业级消息适配器架构，其中包含 MB 中的 ESQL 编程，websphere mq 的配置以及 websphere mq 相关 API 知识。</p>
                    <p>2 该模块主要完成基于 MQ 的 API 下的基于 HTTP 协议的 webservice 调用。</p>
                    <p>3 模块要实现多线程处理以比较高效的处理，详细的日志记录功能。</p>
                    <p>4 开发后期需要进行功能测试以及压力测试。</p>
                </td>
            </tr>
            <tr>
                <td>2008.01—2008.07</td>
                <td>企业运营信息汇集系统</td>
                <td>开发</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>描述：</p>
                    <p>MyEclipse 开发工具</p>
                    <p>PC 服器器硬件环境</p>
                    <p>j2ee，unix 软件环境 1 该项目是为电信运营的 IP 记费服务的，该系统运行于 Tomcat 平台采用 J2EE 架构，Struts 框架，Hibernate,Spring 技术及 Oracle 9i 数据库系统，该系统主要分为用户管理，服务管理，超级管理理管理等。</p>
                    <p>2 此系统后台实现的功能是定期采集始计费日志文件，并将采集的数据封装整理数据清单，然后通过 Socket 上传给中央处理系统；中央处理系统收集信息并通过 Pro*C 将数据存存到 Oracle 数据库当时间间隔对应的 hour_x 表中，最后利用 PL/SQL 周期性进行汇总的整合。</p>
                    <p>3 在本系统中通过 Socket 与 Unix 服务器进行通讯，以实现 Unix 服务器与数据库信息同步。</p>
                </td>
            </tr>
            <tr>
                <td class="section-header">技能特长</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>精通 java 语言开发 ava web ssm，springboot 框架开发，熟练使用 springcloud 分布式框架搭建开发。</p>
                    <p>熟练使用 IBM esb，messagebroker,金蝶 esb 进行 SOA 定制化开发，熟悉 weblogic，websphere 等安装部署，熟悉国产金蝶中间件，了解其性能优化。</p>
                    <p>熟悉物联网技术开发，以及使用 netty 框架进行后台与单片机通信开发。</p>
                    <p>熟练基于 java 的公众号以及小程序开发，了解 android 开发。</p>
                    <p>熟练使用各种 MQ，诸如 ibm wmq，activimq，rabbitmq 等。</p>
                    <p>熟练使用 linux，aix，hp-ux 等操作系统 命令，编译。</p>
                    <p>熟练使用 oracle，mysql 等数据库熟练使用多种 etl 同步工具进行数据同步开发，诸如 kettle，金蝶 etl 等。</p>
                </td>
            </tr>
            <tr>
                <td class="section-header">自我评价</td>
            </tr>
            <tr>
                <td colspan="3">
                    <p>超过 10 年项目及团队管理经验，需求分析，方案设计经验，资深 SOA 架构师，系统集成师，项目管理，曾在中国太平洋保险公司研发部从事基于
                        IBM 产品消息总线项目达 7 年，一年多的物联网企业项目管理经验，熟悉了创业型企业的基本模式，了解到了中国创业型企业的竞争环境。
                    </p>
                </td>
            </tr>
        </table>

        <div class="page-number">5/5</div>
    </div>
</body>
</html>