# RuoYi 前后端分离项目 Docker Compose 部署指南

本文档提供了使用 Docker Compose 部署 RuoYi 前后端分离项目的详细步骤。

## 1. 准备目录结构

在您的项目根目录下创建以下目录和文件：

```
project_root/
├── front/
│   ├── dist/  (前端构建后的文件)
│   └── nginx.conf
├── back/
│   └── cdaid.jar (后端 JAR 文件)
├── db_init/
│   └── cdaid.sql (数据库初始化脚本)
├── Dockerfile-ruoyi-frontend
├── Dockerfile-ruoyi-backend
├── docker-compose-ruoyi.yaml
└── .env
```

-   **front/**: 存放前端相关文件。
    -   **dist/**: 前端项目构建后的文件（例如，Vue.js 项目的 `dist` 目录）。
    -   **nginx.conf**: 前端 Nginx 配置文件。
-   **back/**: 存放后端相关文件。
    -   **cdaid.jar**: 后端 Spring Boot 项目构建后的 JAR 文件。
-   **db_init/**: 存放数据库初始化脚本。
    -   **cdaid.sql**: 数据库初始化 SQL 脚本。
-   **Dockerfile-ruoyi-frontend**: 构建前端镜像的 Dockerfile。
-   **Dockerfile-ruoyi-backend**: 构建后端镜像的 Dockerfile。
-   **docker-compose-ruoyi.yaml**: Docker Compose 配置文件。
-   **.env**: 环境变量配置文件。

## 2. 构建镜像

### 2.1 构建前端镜像

1.  将 `Dockerfile-ruoyi-frontend` 和 `nginx.conf` 文件复制到 `front` 目录的同级目录。
2.  打开终端，进入包含 `Dockerfile-ruoyi-frontend` 的目录。
3.  运行以下命令构建前端镜像：

    ```bash
    docker build -t rouyifrontend:latest -f Dockerfile-ruoyi-frontend .
    ```

### 2.2 构建后端镜像

1.  将 `Dockerfile-ruoyi-backend` 文件复制到 `back` 目录的同级目录。
2.  打开终端，进入包含 `Dockerfile-ruoyi-backend` 的目录。
3.  (可选) 如果存在旧的后端镜像，先删除旧镜像：
    ```bash
    docker rmi rouyibackend:latest
    ```
4.  运行以下命令构建后端镜像：

    ```bash
    docker build -t rouyibackend:latest -f Dockerfile-ruoyi-backend .
    ```

## 3. 运行 Docker Compose

1.  将 `docker-compose-ruoyi.yaml` 和 `.env` 文件放在与 `front`、`back` 和 `db_init` 目录同级的位置。
2.  打开终端，进入包含 `docker-compose-ruoyi.yaml` 和 `.env` 文件的目录。
3.  (可选) 删除旧的容器、网络和数据卷（如果存在）：

    ```bash
    docker-compose -f docker-compose-ruoyi.yaml down -v
    ```
   如果使用了命名volume, 并且需要重新初始化数据库, 执行:
   ```bash
   docker volume rm ruoyi-mysql-data
   ```

4.  运行以下命令启动服务：

    ```bash
    docker-compose -f docker-compose-ruoyi.yaml up -d
    ```

## 4. 查看和管理容器

-   查看所有容器的状态：

    ```bash
    docker-compose -f docker-compose-ruoyi.yaml ps
    ```

-   查看后端容器的日志：

    ```bash
    docker-compose -f docker-compose-ruoyi.yaml logs -f ruoyi-backend
    ```

-   查看 MySQL 容器的日志：

    ```bash
    docker-compose -f docker-compose-ruoyi.yaml logs -f ruoyi-mysql
    ```
- 查看 Redis 容器的日志：
    ```bash
    docker-compose -f docker-compose-ruoyi.yaml logs -f ruoyi-redis
    ```

-   停止服务：

    ```bash
    docker-compose -f docker-compose-ruoyi.yaml down
    ```

## 5. 其他

- 查看docker compose 版本
    ```bash
    docker-compose --version
    ```
- 查看当前目录
    ```bash
    pwd
```

## 7. 完整的部署流程

### 7.1 全新部署

1. 准备工作目录：
```bash
# 创建并进入工作目录
mkdir -p ruoyicompose
cd ruoyicompose

# 创建必要的子目录
mkdir -p front/dist back db_init
```

2. 准备配置文件：
```bash
# 创建 .env 文件
cat > .env << 'EOF'
DB_NAME=cdaid
DB_USER=root
DB_PASSWORD=safiot!QAZ2wsx
REDIS_HOST=ruoyi-redis
REDIS_PORT=6379
MYSQL_ROOT_PASSWORD=safiot!QAZ2wsx
EOF
```

3. 复制项目文件：
```bash
# 复制前端文件
cp -r /path/to/your/frontend/dist/* front/dist/
cp /path/to/your/nginx.conf front/

# 复制后端文件
cp /path/to/your/backend.jar back/cdaid.jar

# 复制数据库初始化脚本
cp /path/to/your/init.sql db_init/cdaid.sql
```

4. 构建镜像：
```bash
# 构建前端镜像
docker build -t rouyifrontend:latest -f Dockerfile-ruoyi-frontend .

# 构建后端镜像
docker build -t rouyibackend:latest -f Dockerfile-ruoyi-backend .
```

5. 启动服务：
```bash
# 启动所有服务
docker-compose -f docker-compose-ruoyi.yaml up -d

# 查看服务状态
docker-compose -f docker-compose-ruoyi.yaml ps
```

### 7.2 更新部署

1. 更新前端：
```bash
# 复制新的前端文件
cp -r /path/to/your/new/dist/* front/dist/

# 重新构建前端镜像
docker build -t rouyifrontend:latest -f Dockerfile-ruoyi-frontend .

# 重启前端服务
docker-compose -f docker-compose-ruoyi.yaml up -d ruoyi-frontend
```

2. 更新后端：
```bash
# 复制新的后端 JAR
cp /path/to/your/new/jar back/cdaid.jar

# 重新构建后端镜像
docker build -t rouyibackend:latest -f Dockerfile-ruoyi-backend .

# 重启后端服务
docker-compose -f docker-compose-ruoyi.yaml up -d ruoyi-backend
docker-compose -f docker-compose-ruoyi.yaml restart ruoyi-backend
```

### 7.3 故障排除

1. 检查网络连接：
```bash
# 检查网络列表
docker network ls

# 检查网络详情
docker network inspect ruoyicompose_default

# 在后端容器中测试连接
docker exec -it ruoyi-backend sh -c "nc -zv ruoyi-mysql 3306"
docker exec -it ruoyi-backend sh -c "nc -zv ruoyi-redis 6379"
```

2. 检查数据库：
```bash
# 连接到数据库
docker exec -it ruoyi-mysql mysql -uroot -p'safiot!QAZ2wsx'

# 检查数据库和表
mysql> USE cdaid;
mysql> SHOW TABLES;
mysql> SELECT user,host FROM mysql.user;
```

3. 检查日志：
```bash
# 检查各服务的日志
docker-compose -f docker-compose-ruoyi.yaml logs -f ruoyi-backend
docker-compose -f docker-compose-ruoyi.yaml logs -f ruoyi-mysql
docker-compose -f docker-compose-ruoyi.yaml logs -f ruoyi-redis
docker-compose -f docker-compose-ruoyi.yaml logs -f ruoyi-frontend
```

4. 重置环境：
```bash
# 完全清理环境
docker-compose -f docker-compose-ruoyi.yaml down -v
#
docker-compose -f docker-compose-ruoyi.yaml down --remove-orphans

docker volume rm ruoyicompose_ruoyi-mysql-data ruoyicompose_ruoyi-redis-data
docker image rm rouyifrontend:latest rouyibackend:latest

# 重新部署
docker-compose -f docker-compose-ruoyi.yaml up -d
```

### 7.4 性能优化

1. 查看资源使用：
```bash
# 查看容器资源使用情况
docker stats

# 查看容器详细信息
docker inspect ruoyi-backend
```

2. 清理资源：
```bash
# 清理未使用的资源
docker system prune -a

# 清理未使用的数据卷
docker volume prune



如果更新了后端 jar 文件或前端文件，请按照以下步骤操作：

**更新后端 jar 文件：**

1.  将新的 `cdaid.jar` 文件替换 `ruoyicompose/back/` 目录下的旧文件。
2.  重新构建后端镜像：`cd ruoyicompose &amp;&amp; docker-compose -f docker-compose-ruoyi.yaml build ruoyi-backend`
3.  重新启动后端服务：`cd ruoyicompose &amp;&amp; docker-compose -f docker-compose-ruoyi.yaml restart ruoyi-backend`

**更新前端文件：**

1.  将新的前端构建文件替换 `ruoyicompose/nginx.conf` 中 `alias` 指定的目录 `/home/<USER>/pro/front/dist/` 下的旧文件。
2.  重新启动前端服务：`cd ruoyicompose &amp;&amp; docker-compose -f docker-compose-ruoyi.yaml restart ruoyi-frontend`

或者, 可以同时重启后端和前端: `cd ruoyicompose &amp;&amp; docker-compose -f docker-compose-ruoyi.yaml restart ruoyi-backend ruoyi-frontend`
```