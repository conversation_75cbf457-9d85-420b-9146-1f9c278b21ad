# 简历

## 基本信息
- **姓名**: 常曹
- **出生年月**: 1983年3月10日
- **民族**: 汉
- **身高**: 170CM
- **电话**: 13771136253
- **政治面貌**: 白身
- **邮箱**: chang<PERSON><EMAIL>
- **毕业院校**: 常州大学
- **住址**: 江苏无锡新区
- **学历**: 本科

## 求职意向
- **部门**: 主管、系统架构
- **入职时间**: 一个月内

## 教育背景
- **2004.09-2007.06**: 常州大学学院计算机科学与技术（本科）

## 工作经验
### 2018.01—至今
- **公司**: 无锡萨弗特智能科技有限公司
- **职位**: 研发部门主管
- **职责**:
  - 部门管理，技术牵头。
  - 物联网产品规划，设计，软件架构。
  - 项目需求分析，管理开发跟踪。
  - 外部项目沟通，资源利用。

### 2014.05—2018.11
- **公司**: 金蝶集团
- **职位**: 项目管理技术架构
- **职责**:
  - 负责软件需求调研、项目任务书、软件需求规格说明书的编写及确认。
  - 技术框架选型，原型设计讨论确认，技术疑问攻关。
  - 负责软件项目整体实施的计划的编制、核实、确认和发布，跟踪与监控软件项目的实施进度，确保项目按计划执行。
  - 完成整个项目软件部分以及与硬件接口部分的实施并确保项目的最终验收。
  - 负责项目履行过程中与用户，供应商，集成商，公司其它相关部门关于公司自主产品技术问题和管理问题的协调和沟通。

### 2007.09—2014.05
- **公司**: 中国太平洋保险公司外派
- **职位**: 项目管理技术开发
- **职责**:
  - 项目管理
  - 团队管理
  - 软件开发

## 项目经验
### 2018.11—至今
- **项目**: 企业 CMMI3 评选项目
- **职位**: 项目总负责
- **描述**:
  - CMMI3认证意义：企业能够对项目的实施有一整套的管理措施，并保障项目的完成；企业能够根据自身特殊情况以及自己的标准流程，将这套管理体系与流程予以制度化，不仅能够在同类的项目上得到成功的实施，在不同类的项目上一样能够得到成功的实施。科学的管理成为企业的文化，企业的组织财富。
- **责任**:
  - 负责整个项目的定义工作安排，培训接洽，人员组织，技术人员帅选。
  - 负责参评项目选型，项目文档规范构建补充。
  - 负责参评现场的统筹安排，制定过CMMI3的具体流程。

### 2018.09—2018.11
- **项目**: 新零售无人称重货柜物联网项目（离线版）
- **职位**: 项目管理技术架构
- **描述**:
  - 无人智能货柜离线版为无人智能货柜在线版的客户定制化需求补充。为了满足客户无电要求而定制化改造的项目。主要改造点在于结合手机APP二维码扫码技术以及密钥SDK比对技术对在线版本进行改造。单片机部分实行2G通信模块移除，加入二维码生成算法以及密钥生成算法。
- **责任**:
  - 项目管理，需求制定，进度跟踪等。

### 2018.01—2018.11
- **项目**: 新零售无人称重货柜物联网项目（在线版）
- **职位**: 项目管理技术架构
- **描述**:
  - 无人智能货柜是依托物联网技术而兴起的新零售模式，而无人智能货柜平台将实现无人智能货柜线上线下智能管理，数据分析，业务监控等，实现智能化新零售。依托物联网实现智能化购物，无人化管理。智能终端通过各种传感器收集用户购物行为进行，如称重数据，报价预判，关门等状态收集。移动端以小程序技术开发，用于客户选购商品，动态展示商品增减，商品支付，报警跟踪等。后台技术架构主要为springboot+dubbo实现商家管理，预售商品登记，支付设置，支付提现，信息统计图表等。硬件通信采用netty框架，基于自定义的硬件协议配合密钥SDK（jni编译C语言函数）机制进行安全加密实现硬件与后台的通信。
- **技术栈**:
  - Maven管理构建项目。
  - thymeleaf、bootstrap、layer等框架完成前端展示功能
  - MyBatisDruid等持久层框架。
  - 百度webuploader、EChars完成文件上传和图片展示
  - quatarz完成消费者未支付订单及提醒功能及其他系统调度任务，采用redis缓存技术提高系统效率
  - Shiro完成数据权限信息管理。
  - 依托springAOP技术实现多数据源切换。
  - RabbitMQ队列技术实现发布订阅，减少远程RPC压力，优化性能。
  - 交付部署采用docker虚拟化方式一键配置部署服务，实现灵活化部署。
- **责任**:
  - 从项目立项到项目技术架构再到项目进度成本跟踪最后项目测试交付的一条龙管理。不限于项目的框架搭建，技术选型，疑难攻关已经后期性能优化。

### 2015.01—2017.05
- **项目**: 南昌人社 SOA 数据集成交换平台
- **职位**: 项目管理技术架构
- **描述**:
  - 1. 南昌人社局信息中心的系统安全、性能、软硬件配置等整体规划，在软硬件、网络等基础设施方面已经具备了一套比较全面的从物理层到管理层的完整安全、性能监控体系。对南昌人社内现有业务系统进行数据及服务集成，并在统一的集成框架下，补充现有产品中不足的业务功能，及基于中间件产品的二次开发,最终将形成“一网一平台”的基本业务功能框架。
  - 2. 技术产品选型金蝶ESB设计器,开发工具,eclipse,soapui,开发工具 linux，AESB,CXF,oracle，软件环境标准SOA规范的制订，统一社保局多个系统间的接口规范！建设SOA应用集成平台，完成服务资产目录和服务库管理。
  - 3. 项目模块划分，指定项目进度周期表，项目进度跟踪，项目质量保证等。
  - 4. 项目一期完成后参与二期建设工作的讨论等。

### 2016.01—2016.08
- **项目**: 上图馆数据集成项目
- **职位**: 项目管理技术架构
- **描述**:
  - 上海经信委合作项目，设计上图馆现有项目之间信息孤岛打通，采用总线型机构设计。应用金蝶ESB服务总线产品对上海图书馆各个系统数据实现集成，通过其提供的各种输入输出及转换组件，从各种输入介质如Text文件、XML、Excel、DBMS的表中获取源数据进行初始采集和增量采集、处理。企业应用服务注册,服务编排,应用协议转换对多个服务进行编排组合，并根据服务请求方的请求内容来进行消息路由，路由至不同的服务提供方。

### 2013.03—2014.05
- **项目**: 上海浦东公交系统财务数据中⼼
- **职位**: 项目管理技术架构
- **描述**:
  - 金蝶ESB设计器,开发工具 前期业务梳理,数据整理。金蝶ESB，金蝶MQ，sqlserver2008软件环境打破原有上海某公交财务系统各个分部之间的数据孤立状态，成功的使其连成一线，完成数据间的逻辑ETL操作，促成统一网状架构。

### 2013.03—2014.05
- **项目**: 坐席运营系统
- **职位**: 项目管理开发
- **描述**:
  - 系统包括坐席系统部分和运营管理，知识库等部分。坐席系统包括软电话呼叫中心，通用业务，产险业务受理，产险业务查询，产险回访，寿险务受理，寿险业务查询，寿险回访。运营管理包含诸多产寿险管理条目如机构管理，品牌管理，回访管理等。
  - 技术上基于SPING,WEBWORK,HIBERNATE,中间件基于was,wmq，数据库基于ORACLE.
  - 开发进度上，本阶段属于项目的二期，主要是整合多个运营管理系统。

### 2012.07—2013.02
- **项目**: 在线出单系统
- **职位**: 项目管理开发
- **描述**:
  - 在线出单系统为太平洋寿险各个分公司时时线上出单系统。拥有涵盖寿险几乎所有险种。与诸多系统对接，实现其总打印平台功能。数据分时时与批量等方式与寿险核心同步，保单生效等。新险种持续对接入改平台从而从分工单打印，同步入总公司集团。

### 2012.04—2012.07
- **项目**: 数据交换系统后台管理
- **职位**: 项目管理开发
- **描述**:
  - 为适应新平台不断增加的接口业务需要，以及旧管理系统不断升高的维护成本而重写此后台管理系统。基于SSH框架，前台页面显示基于EXTJS框架+JSON+JSP，后台数据库为oracle,应用服务器为was。新系统对于产，寿险通道申请区别处理，增加通道时时维护，监视等功能，解放维护人员大量工作量。增加相关自选项，减少与业务人员沟通工作量，使得系统更具有人性化，自动化。

### 2011.08—2012.03
- **项目**: WebSphereESB系统持续改造
- **职位**: 项目管理开发
- **描述**:
  - WIDwebshpereintergrationdeveloper，ECLIPS开发工具 AIX硬件环境 websphereesb软件环境基于产险ESB内部消息交互系统改造。
  - 包括
  - 1）原有项目程序重新设计，代码重新精简，功能重新规划。
  - 2）监控系统进一步升级，完善。
  - 3）消息路由模块重构，整合。
  - 4）消息解析模块重构，整合。
  - 5）预生产环境压力评估系统开发。

### 2010.04—2011.11
- **项目**: 文件数据交互器
- **职位**: 项目管理开发
- **描述**:
  - visualc++，vi开发工具 HP-UX小型机硬件环境 C软件环境1为弥补消息交换总线传特大文件上性能的不足的问题，开发用于点对点的文件传输适配器。
  - 2适配器基于C语言，可成功运行于WINDOWS，LINUX，HP-UX，以及AIX等操作系统。
  - 3本模块基于TCP-IP下的SOCKET编程，采用多线程机制，线性日志模式，以及必要的备份功能。
  - 4重要功能则是可以确保文件分批次发送，采用TRIG机制。

### 2011.04—2011.06
- **项目**: 数据交互中心监控改造
- **职位**: 项目管理开发
- **描述**:
  - vi开发工具 小型机硬件环境 HP-UX，shell脚本软件环境进一步完善关于消息交换中间件WMQ的相关监控系统。针对业务量大的系统，进行通道状态以及队列深度的全天监控，并且与短信系统相关接口对接，以达到发短信监控的目的。

### 2011.01—2011.05
- **项目**: 消息交换总线核心架构改造
- **职位**: 项目管理开发
- **描述**:
  - visualc++，vi开发工具 HP-UX小型机硬件环境 C软件环境为适应新的不断增加的消息信息量，解决原有热备单工方式存在过多资源浪费，处理能比达到瓶颈等问题，现要求以中心集群方式重新部署整个SOA中心系统，实现MB双工，MQ实现集群，前置通讯队列管理器热备的架构。

### 2010.03—2010.11
- **项目**: 企业级数据交互功能（PUBSUB）
- **职位**: 项目开发
- **描述**:
  - MyEclipse，visualc++，vi开发工具 HP-UX小型机硬件环境 JAVA,C软件环境2010.3-2010.11
  - 1为适应新需求，已经充分发挥MB的发布订阅功能，需要开发新的适配器接口,并且改版本拥有C,java两种版本。
  - 2模块功能分别采用JAVA以及C语言编写，以适应不同的请求方或者响应方。
  - 3模块依赖于websphereMB中心代理进行主题树的生成与管理，控制流队列对于代理的控制而保持其发布订阅功能。
  - 4编写适配器，增加PUBSUB接口，实现MQAPI，进而达到能够操作中心主题数，发布主题，订阅主题。

### 2009.06—2010.01
- **项目**: 消息交互总线支持功能增加（TUXEDO）
- **职位**: 开发
- **描述**:
  - vi，visualc++开发工具 HP-UX小型机硬件环境 c语言，HP-UX软件环境
  - 1为适消息响应方的TUXEDO服务，为消息交换系统增加TUXEDO调用客户端。
  - 2该模块是在HP-UX操作系统下基于websphereMQapi采用C语言编写。
  - 3具体功能负责从MQ队列中取走消息并且调用TUX服务。
  - 4实现中采用多进程线性日志编写法。
  - 5后期测试以及功能修改维护等。

### 2008.08—2009.05
- **项目**: 企业消息交换总线（IBMSOA）
- **职位**: 开发
- **描述**:
  - MyEclipse开发工具 HP-UX小型机硬件环境 JAVA软件环境1该项目是基于IBMwebspheremessagequeue,IBMwebspheremessagebroker的一套企业级别消息交换框架。其中包含MB中的ESQL编程，webshperemq的配置以及webshperemq相关API知识。
  - 2该模块主要完成基于MQ的API下的基于HTTP协议的webservice调用。
  - 3模块要实现多线程处理已经比较灵活的异常处理，详细的日志记录功能。
  - 4开发后期需要进行功能测试以及压力测试。

### 2008.01—2008.07
- **项目**: 企业运营信息汇集系统
- **职位**: 开发
- **描述**:
  - MyEclipse开发工具 PC服务器硬件环境 j2ee，unix软件环境1该项目是为电信运营的IP记费服务的。该系统运行于Tomcat平台,采用J2EE架构、Struts框架，Hibernate,Spring技术及Oracle9i数据库系统。该系统主要分为用户自服务管理，管理员管理，超级管理员管理等模块。
  - 2此系统后台实现的功能是定期采集原始计费日志文件，并将采集的数据封装整理数据清单，然后通过Socket上传给中央处理系统；中央处理系统收集信息并通过Pro*C将数据保存到Oracle数据库当前时间所对应的hour_x表中，最后利用PL/SQL周期性进行数据的整合。
  - 3在本系统中通过Socket与Unix服务器进行通讯,以实现Unix服务器与数据库信息同步。

## 技能特长
- 精通java语言开发avawebssm，springboot框架开发，熟练使用springcloud分布式框架搭建开发。
- 熟练使用IBMesb，messagebroker,金蝶esb进行SOA定制化开发，熟悉weblogic，websphere等安装部署，熟悉国产金蝶中间件，了解其性能优化。
- 熟悉物联网技术开发，以及使用netty框架进行后台与单片机通信开发。
- 熟悉基于java的公众号以及小程序开发，了解androd开发。
- 熟练使用多种MQ，诸如ibmwmq，activimq，rabbitmq等。
- 熟练使用linux，aix，hp-ux等。熟练使用shell命令，编程。
- 熟练使用oracle，mysql等数据库熟练使用多种etl同步工具进行数据同步开发，诸如kettle，金蝶etl等。

## 自我评价
- 超过10年项目及团队管理经验，需求分析，方案设计经验。资深SOA架构师，系统集成师，项目管理。曾在中国太平洋保险公司研发部从事基于IBM产品消息总线项目达7年。
- 一年多的物联网企业项目管理经验，熟悉了创业型企业的基本模式，了解了中国创业型企业的竞争环境。