# For more information on configuration, see:
#   * Official English Documentation: http://nginx.org/en/docs/
#   * Official Russian Documentation: http://nginx.org/ru/docs/

user root;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 1024;
}

http {
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 2048;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    # Load modular configuration files from the /etc/nginx/conf.d directory.
    # See http://nginx.org/en/docs/ngx_core_module.html#include
    # for more information.
    include /etc/nginx/conf.d/*.conf;
	client_header_buffer_size 128k;

	large_client_header_buffers 4 128k;
    server {
        #SSL 默认访问端口号为 443
        listen 443 ssl; 
        #请填写绑定证书的域名
        server_name jesonliye.com; 
        #请填写证书文件的相对路径或绝对路径
        ssl_certificate jesonliye.com_bundle.crt; 
        #请填写私钥文件的相对路径或绝对路径
        ssl_certificate_key jesonliye.com.key; 
        ssl_session_timeout 5m;
        #请按照以下协议配置
        ssl_protocols TLSv1.2 TLSv1.3; 
        #请按照以下套件配置，配置加密套件，写法遵循 openssl 标准。
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE; 
        ssl_prefer_server_ciphers on;
        location /aiimages {
            proxy_pass http://***************:3303/images/;
	   }
        location / {
            #网站主页路径。此路径仅供参考，具体请您按照实际目录操作。
            #例如，您的网站主页在 Nginx 服务器的 /etc/www 目录下，则请修改 root 后面的 html 为 /etc/www。
            #root /home/<USER>/face/jiesheng; 
            #index  jeson.html jeson.htm;
	    set $is_matched 0;
	    if ($host = jesonliye.com) {
                #proxy_pass https://jesonliye.com;
		proxy_pass https://**************:8333;

                set $is_matched 1;
	       }
	    # 没有匹配到，跳转到默认页面
            if ($is_matched = 0) {
                proxy_pass http://localhost:8023;
               }
            }

    }

    server {
        listen       8023 default_server;
        #listen       [::]:80 default_server;
        server_name  _;
        root         /home/<USER>/face/jiesheng;

        # Load configuration files for the default server block.
        include /etc/nginx/default.d/*.conf;

        location / {
	index	     jeson.html jeson.htm;
        }

        error_page 404 /404.html;
            location = /40x.html {
        }

        error_page 500 502 503 504 /50x.html;
            location = /50x.html {
        }
    }
    server {
        listen       2918;
        server_name  _;
        root         /home/<USER>/face/tt/big;

        # Load configuration files for the default server block.
        include /etc/nginx/default.d/*.conf;

        location / {
	index	     index.html jeson.htm;
        }

        error_page 404 /404.html;
            location = /40x.html {
        }

        error_page 500 502 503 504 /50x.html;
            location = /50x.html {
        }
    }
	server {
		listen 8011; # 根据需求选择合适的端口号
	
		location /api {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;


        # 隐藏真实 IP 地址的请求头
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Server $host;
        proxy_set_header X-Forwarded-Proto $scheme;
		proxy_hide_header httpurl;

		proxy_pass http://*************:9701/iot/eventSystem/eventInfo/receiveEvent2;
		}
	}
    server {
        listen       80;
        server_name  *.jesonliye.com;

        # Load configuration files for the default server block.
        include /etc/nginx/default.d/*.conf;

        location / {
		set $is_matched 0;
		if ($host = xpark.jesonliye.com) {
            proxy_pass http://jesonliye.com:3302;
            set $is_matched 1;
	   }
        if ($host = xparkmobile.jesonliye.com) {
            proxy_pass http://jesonliye.com:2203;
            set $is_matched 1;
	   }
        if ($host = testcc1.jesonliye.com) {
            proxy_pass http://jesonliye.com:3111;
            set $is_matched 1;
	   }
	   if ($host = testcc2.jesonliye.com) {
            proxy_pass http://jesonliye.com:3112;
            set $is_matched 1;
	   }
	   if ($host = testcc3.jesonliye.com) {
            proxy_pass http://jesonliye.com:3113;
            set $is_matched 1;
	   }
	# 没有匹配到，跳转到默认页面
        if ($is_matched = 0) {
            proxy_pass http://localhost:8023;
           }
        }



	location ^~/nokia {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass http://localhost:8024/nokia/;
        }




	#location ~ .*\.(js|css)?$ {
	#    expires 12h;
	#    proxy_pass http://jesonliye.com:3302;
	#}
	#location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)?$ {
	#    expires 12h;
	#    proxy_pass http://jesonliye.com:3302;
	#}
	
	location ^~/jesonmobile/ {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass http://localhost:6333/;
        }

        error_page 404 /404.html;
            location = /40x.html {
        }

        error_page 500 502 503 504 /50x.html;
            location = /50x.html {
        }
    }

    server {
        listen       6333;
        server_name  _;
        root         /home/<USER>/face/mobile/jesonMobile;

        # Load configuration files for the default server block.
        include /etc/nginx/default.d/*.conf;

        location / {
	index	     index.html main.html;
        }

        error_page 404 /404.html;
            location = /40x.html {
        }

        error_page 500 502 503 504 /50x.html;
            location = /50x.html {
        }
    }


    server {
        listen       18333;
	location / {
	    proxy_pass http://************:8333;
        }
	
    }

	server{
		listen 2100;
		server_name  aiimg;
		location /images {
		    root  /root/rubbish/data/;
		 #   autoindex on;
		}  		
	} 
# Set	gs for a TLS enabled server.
#
#    server {
#        listen       443 ssl http2 default_server;
#        listen       [::]:443 ssl http2 default_server;
#        server_name  _;
#        root         /usr/share/nginx/html;
#
#        ssl_certificate "/etc/pki/nginx/server.crt";
#        ssl_certificate_key "/etc/pki/nginx/private/server.key";
#        ssl_session_cache shared:SSL:1m;
#        ssl_session_timeout  10m;
#        ssl_ciphers HIGH:!aNULL:!MD5;
#        ssl_prefer_server_ciphers on;
#
#        # Load configuration files for the default server block.
#        include /etc/nginx/default.d/*.conf;
#
#        location / {
#        }
#
#        error_page 404 /404.html;
#            location = /40x.html {
#        }
#
#        error_page 500 502 503 504 /50x.html;
#            location = /50x.html {
#        }
#    }

}

