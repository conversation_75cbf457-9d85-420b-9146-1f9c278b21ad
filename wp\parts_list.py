import pandas as pd

# 创建零件清单数据
data = {
    '序号': [8, 7, 6, 5, 4, 3, 2],
    '零件编号': ['ISO 15979', '8007P17', 'WL-04-D', 'IL-09-XXXX*XXXX', '8007P09', 'DIN127-B', '8007P05'],
    '版本': ['', '01', '', '', '01', '', '03'],
    '描述': ['blind rivet 4.8x20', 'corrugated metal gasket with three peaks m5', 'warning sticker', 
            'brand & size sticker', 'spacer', 'spring lock washer', 'wind latch'],
    '数量': [2, 2, 1, 2, 2, 2, 2],
    '材料/标准': ['STEEL(BODY)/STEEL(MANDREL)', '65Mn-GBT 1222', 'REFER TO QR-123', 'REFER TO QR-123',
                'S235JR-EN10025-2', '65Mn-GB/T 1222', 'S235JR-EN10025-2']
}

# 创建DataFrame
df = pd.DataFrame(data)

# 保存为Excel文件
df.to_excel('parts_list.xlsx', index=False)