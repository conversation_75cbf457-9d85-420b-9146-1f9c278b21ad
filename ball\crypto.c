#include "crypto.h"
#include <iphlpapi.h>
#include <stdio.h>

#define LICENSE_FILE "license.dat"
#define ENCRYPTION_KEY 0x12345678

static DWORD CalculateChecksum(const LICENSE_DATA* data)
{
    DWORD hash = ENCRYPTION_KEY;
    const BYTE* ptr = (const BYTE*)data;
    for(size_t i = 0; i < sizeof(LICENSE_DATA) - sizeof(DWORD); i++) {
        hash = ((hash << 5) + hash) + ptr[i];
    }
    return hash;
}

void GetMachineID(char* machineId)
{
    DWORD hash = 0;
    
    // Get MAC address
    IP_ADAPTER_INFO AdapterInfo[16];
    DWORD dwBufLen = sizeof(AdapterInfo);
    if (GetAdaptersInfo(AdapterInfo, &dwBufLen) == ERROR_SUCCESS) {
        PIP_ADAPTER_INFO pAdapterInfo = AdapterInfo;
        for(int i = 0; i < 6; i++) {
            hash = hash * 31 + pAdapterInfo->Address[i];
        }
    }
    
    // Get volume serial
    char volumeName[MAX_PATH];
    char fileSystem[MAX_PATH];
    DWORD serialNumber;
    GetVolumeInformationA("C:\\", volumeName, MAX_PATH, &serialNumber, 
                         NULL, NULL, fileSystem, MAX_PATH);
    hash = hash * 31 + serialNumber;
    
    // Get computer name
    char computerName[MAX_COMPUTERNAME_LENGTH + 1];
    DWORD size = sizeof(computerName);
    if (GetComputerNameA(computerName, &size)) {
        for(DWORD i = 0; i < size; i++) {
            hash = hash * 31 + computerName[i];
        }
    }
    
    // Generate machine ID
    for(int i = 0; i < MACHINE_ID_LENGTH - 1; i++) {
        hash = hash * ********** + 12345;
        machineId[i] = ((hash >> 16) & 0x1F) + 'A';
    }
    machineId[MACHINE_ID_LENGTH - 1] = '\0';
}

void GenerateActivationCode(const char* machineId, int uses, char* activationCode)
{
    LICENSE_DATA data = {0};
    strcpy(data.machineId, machineId);
    data.maxUses = uses;
    data.expiryDate = (DWORD)time(NULL) + (90 * 24 * 60 * 60); // 90 days
    data.checksum = CalculateChecksum(&data);
    
    // Encode license data to activation code
    DWORD* ptr = (DWORD*)&data;
    for(int i = 0; i < sizeof(LICENSE_DATA)/4; i++) {
        sprintf(activationCode + (i * 6), "%06X", ptr[i] ^ ENCRYPTION_KEY);
    }
}

BOOL ValidateActivationCode(const char* activationCode, LICENSE_DATA* licenseData)
{
    // Decode activation code to license data
    LICENSE_DATA data = {0};
    DWORD* ptr = (DWORD*)&data;
    for(int i = 0; i < sizeof(LICENSE_DATA)/4; i++) {
        sscanf(activationCode + (i * 6), "%06X", &ptr[i]);
        ptr[i] ^= ENCRYPTION_KEY;
    }
    
    // Validate checksum
    DWORD checksum = data.checksum;
    data.checksum = 0;
    if(checksum != CalculateChecksum(&data)) {
        return FALSE;
    }
    
    // Copy validated data
    memcpy(licenseData, &data, sizeof(LICENSE_DATA));
    return TRUE;
}

BOOL SaveLicenseData(const LICENSE_DATA* licenseData)
{
    FILE* file = fopen(LICENSE_FILE, "wb");
    if(!file) return FALSE;
    
    fwrite(licenseData, sizeof(LICENSE_DATA), 1, file);
    fclose(file);
    return TRUE;
}

BOOL LoadLicenseData(LICENSE_DATA* licenseData)
{
    FILE* file = fopen(LICENSE_FILE, "rb");
    if(!file) return FALSE;
    
    BOOL result = (fread(licenseData, sizeof(LICENSE_DATA), 1, file) == 1);
    fclose(file);
    return result;
} 