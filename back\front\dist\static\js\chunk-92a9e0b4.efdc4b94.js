(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-92a9e0b4"],{"2d69":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"名称",prop:"name"}},[r("el-input",{attrs:{placeholder:"请输入名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),r("el-form-item",{attrs:{label:"IP",prop:"code"}},[r("el-input",{attrs:{placeholder:"请输入IP",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.code,callback:function(t){e.$set(e.queryParams,"code",t)},expression:"queryParams.code"}})],1),r("el-form-item",{attrs:{label:"区库",prop:"deptId"}},[r("el-select",{attrs:{placeholder:"请选择区库",clearable:"",filterable:""},on:{change:e.handleQuery},model:{value:e.queryParams.deptId,callback:function(t){e.$set(e.queryParams,"deptId",t)},expression:"queryParams.deptId"}},e._l(e.deptOptions,(function(e,t){return r("el-option",{key:t,attrs:{label:e.text,value:e.value}})})),1)],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["device:rfid:add"],expression:"['device:rfid:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["device:rfid:export"],expression:"['device:rfid:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出 ")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.rfidList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{label:"名称",align:"center",prop:"name"}}),r("el-table-column",{attrs:{label:"IP",align:"center",prop:"code"}}),r("el-table-column",{attrs:{label:"区库",align:"center",prop:"deptName"}}),r("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.common_status,value:t.row.status}})]}}])}),r("el-table-column",{attrs:{label:"运行状态",align:"center",prop:"runStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.rfid_run_status,value:t.row.runStatus}})]}}])}),r("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return["0"===t.row.runStatus?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["device:rfid:edit"],expression:"['device:rfid:edit']"}],staticStyle:{color:"#ff0000"},attrs:{size:"mini",type:"text",icon:"el-icon-video-pause"},on:{click:function(r){return e.stop(t.row.id)}}},[e._v("停止 ")]):e._e(),"1"===t.row.runStatus?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["device:rfid:edit"],expression:"['device:rfid:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-video-play"},on:{click:function(r){return e.start(t.row.id)}}},[e._v("运行 ")]):e._e(),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["device:rfid:edit"],expression:"['device:rfid:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),"1"===t.row.status?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["device:rfid:remove"],expression:"['device:rfid:remove']"}],staticStyle:{color:"#ff0000"},attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除 ")]):e._e()]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"名称",prop:"name"}},[r("el-input",{attrs:{placeholder:"请输入名称",maxlength:"50","show-word-limit":""},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),r("el-form-item",{attrs:{label:"区库",prop:"deptId"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择区库",clearable:"",filterable:""},model:{value:e.form.deptId,callback:function(t){e.$set(e.form,"deptId",t)},expression:"form.deptId"}},e._l(e.deptOptions,(function(e,t){return r("el-option",{key:t,attrs:{label:e.text,value:parseInt(e.value)}})})),1)],1),r("el-form-item",{attrs:{label:"IP",prop:"code"}},[r("el-input",{attrs:{placeholder:"请输入IP",maxlength:"50","show-word-limit":""},model:{value:e.form.code,callback:function(t){e.$set(e.form,"code",t)},expression:"form.code"}})],1),r("el-form-item",{attrs:{label:"状态",prop:"status"}},[r("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.common_status,(function(t){return r("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label)+" ")])})),1)],1),r("el-form-item",{attrs:{label:"备注",prop:"remark"}},[r("el-input",{attrs:{type:"textarea",placeholder:"请输入内容"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},a=[],i=r("5530"),o=(r("d81d"),r("b775"));function l(e){return Object(o["a"])({url:"/device/rfid/list",method:"get",params:e})}function s(e){return Object(o["a"])({url:"/device/rfid/start",method:"get",params:e})}function u(e){return Object(o["a"])({url:"/device/rfid/stop",method:"get",params:e})}function c(e){return Object(o["a"])({url:"/device/rfid/"+e,method:"get"})}function d(e){return Object(o["a"])({url:"/device/rfid",method:"post",data:e})}function m(e){return Object(o["a"])({url:"/device/rfid",method:"put",data:e})}function p(e){return Object(o["a"])({url:"/device/rfid/"+e,method:"delete"})}var f=r("fcb7"),h={name:"Rfid",dicts:["common_status","rfid_run_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,rfidList:[],deptOptions:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,name:null,code:null,deptId:null},form:{},rules:{name:[{required:!0,message:"名称不能为空",trigger:"blur"}],code:[{required:!0,message:"IP不能为空",trigger:"blur"}],deptId:[{required:!0,message:"区库不能为空",trigger:"change"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,l(this.queryParams).then((function(t){e.rfidList=t.rows,e.total=t.total,e.loading=!1})),this.getDeptOption()},getDeptOption:function(){var e=this;Object(f["f"])({type:"1"}).then((function(t){e.deptOptions=t.data}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,name:null,code:null,deptId:null,status:"0",createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加RFID设备",this.getDeptOption()},handleUpdate:function(e){var t=this;this.reset();var r=e.id||this.ids;c(r).then((function(e){t.form=e.data,t.open=!0,t.title="修改RFID设备"})),this.getDeptOption()},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?m(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):d(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.id||this.ids;this.$modal.confirm('是否确认删除RFID设备编号为"'+r+'"的数据项？').then((function(){return p(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},start:function(e){var t=this;this.$modal.confirm("是否确认启动设备？").then((function(){return s({id:e})})).then((function(){t.getList(),t.$modal.msgSuccess("操作成功")})).catch((function(){}))},stop:function(e){var t=this;this.$modal.confirm("是否确认停止设备？").then((function(){return u({id:e})})).then((function(){t.getList(),t.$modal.msgSuccess("操作成功")})).catch((function(){}))},handleExport:function(){this.download("device/rfid/export",Object(i["a"])({},this.queryParams),"rfid_".concat((new Date).getTime(),".xlsx"))}}},v=h,b=r("2877"),g=Object(b["a"])(v,n,a,!1,null,null,null);t["default"]=g.exports},fcb7:function(e,t,r){"use strict";r.d(t,"f",(function(){return a})),r.d(t,"d",(function(){return i})),r.d(t,"e",(function(){return o})),r.d(t,"c",(function(){return l})),r.d(t,"a",(function(){return s})),r.d(t,"g",(function(){return u})),r.d(t,"b",(function(){return c}));var n=r("b775");function a(e){return Object(n["a"])({url:"/system/dept/option",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/system/dept/list",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/system/dept/list/exclude/"+e,method:"get"})}function l(e){return Object(n["a"])({url:"/system/dept/"+e,method:"get"})}function s(e){return Object(n["a"])({url:"/system/dept",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/system/dept",method:"put",data:e})}function c(e){return Object(n["a"])({url:"/system/dept/"+e,method:"delete"})}}}]);