import requests
import json
from datetime import datetime, timedelta
import time

# 配置信息
SERVER_IP = "127.0.0.1"  # 修改为你的服务器IP
GELF_PORT = "12201"
API_PORT = "9000"
USERNAME = "admin"
PASSWORD = "msgbus!QAZ2wsx"  # 修改为你的密码

def send_message():
    """发送测试消息"""
    send_url = f"http://{SERVER_IP}:{GELF_PORT}/gelf"
    test_data = {
        "version": "1.1",
        "host": "test-host",
        "short_message": f"测试消息-{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "level": 1,
        "_source": "test-source"
    }

    print("发送消息...")
    try:
        send_response = requests.post(send_url, json=test_data)
        print(f"发送状态: {send_response.status_code}")
        return send_response.status_code == 202
    except Exception as e:
        print(f"发送失败: {e}")
        return False

def query_messages():
    """查询消息"""
    graylog_url = f"http://{SERVER_IP}:{API_PORT}/api"
    params = {
        'query': 'source:test-source',
        'range': 300,
        'limit': 10,
        'sort': 'timestamp:desc'
    }

    print("\n查询消息...")
    try:
        response = requests.get(
            f"{graylog_url}/search/universal/relative",
            auth=(USERNAME, PASSWORD),
            params=params
        )

        if response.status_code == 200:
            messages = response.json().get('messages', [])
            print(f"\n找到 {len(messages)} 条消息:")
            for msg in messages:
                print("\n消息详情:")
                print(json.dumps(msg, indent=2, ensure_ascii=False))
        else:
            print(f"查询失败: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"查询失败: {e}")

def main():
    # 发送消息
    if send_message():
        # 等待消息处理
        print("等待消息处理...")
        time.sleep(2)
        # 查询消息
        query_messages()
    else:
        print("消息发送失败，停止查询")

if __name__ == "__main__":
    main() 