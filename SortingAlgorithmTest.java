import java.util.*;
import java.util.stream.IntStream;

/**
 * 排序算法测试类
 * 包含正确性测试、性能测试、边界测试等
 */
public class SortingAlgorithmTest {
    
    private static final String GREEN = "\u001B[32m";
    private static final String RED = "\u001B[31m";
    private static final String BLUE = "\u001B[34m";
    private static final String YELLOW = "\u001B[33m";
    private static final String RESET = "\u001B[0m";
    private static final String BOLD = "\u001B[1m";
    
    public static void main(String[] args) {
        System.out.println(BOLD + BLUE + "🚀 排序算法完整测试套件" + RESET);
        System.out.println("=" * 50);
        
        // 运行所有测试
        runCorrectnessTests();
        runBoundaryTests();
        runPerformanceTests();
        runStabilityTests();
        
        System.out.println(BOLD + GREEN + "\n✅ 所有测试完成！" + RESET);
    }
    
    /**
     * 正确性测试 - 验证排序结果是否正确
     */
    public static void runCorrectnessTests() {
        System.out.println(BOLD + YELLOW + "\n📋 正确性测试" + RESET);
        System.out.println("-" * 30);
        
        // 测试数据集
        int[][] testCases = {
            {64, 34, 25, 12, 22, 11, 90, 5, 77, 30},           // 随机数组
            {1, 2, 3, 4, 5, 6, 7, 8, 9, 10},                   // 已排序
            {10, 9, 8, 7, 6, 5, 4, 3, 2, 1},                   // 逆序
            {5, 5, 5, 5, 5, 5, 5, 5, 5, 5},                    // 相同元素
            {1, 3, 2, 3, 1, 2, 3, 1, 2, 1},                    // 重复元素
            generateRandomArray(100),                           // 中等随机数组
            generateRandomArray(1000)                           // 大随机数组
        };
        
        String[] testNames = {
            "随机数组", "已排序数组", "逆序数组", "相同元素", 
            "重复元素", "中等随机数组", "大随机数组"
        };
        
        for (int i = 0; i < testCases.length; i++) {
            System.out.println("\n测试案例: " + testNames[i]);
            testAllAlgorithms(testCases[i]);
        }
    }
    
    /**
     * 边界测试 - 测试特殊情况
     */
    public static void runBoundaryTests() {
        System.out.println(BOLD + YELLOW + "\n🔍 边界测试" + RESET);
        System.out.println("-" * 30);
        
        // 空数组
        System.out.println("\n测试: 空数组");
        testAllAlgorithms(new int[]{});
        
        // 单元素
        System.out.println("\n测试: 单元素数组");
        testAllAlgorithms(new int[]{42});
        
        // 两元素
        System.out.println("\n测试: 两元素数组");
        testAllAlgorithms(new int[]{2, 1});
        
        // 大量重复
        System.out.println("\n测试: 大量重复元素");
        int[] manyDuplicates = new int[1000];
        Arrays.fill(manyDuplicates, 42);
        testAllAlgorithms(manyDuplicates);
    }
    
    /**
     * 性能测试 - 比较各算法性能
     */
    public static void runPerformanceTests() {
        System.out.println(BOLD + YELLOW + "\n⚡ 性能测试" + RESET);
        System.out.println("-" * 30);
        
        int[] sizes = {1000, 5000, 10000, 50000, 100000};
        
        for (int size : sizes) {
            System.out.println(String.format("\n📊 数组大小: %,d", size));
            System.out.println("-" * 40);
            
            // 生成测试数据
            int[] randomData = generateRandomArray(size);
            int[] sortedData = IntStream.range(0, size).toArray();
            int[] reversedData = IntStream.range(0, size).map(i -> size - i).toArray();
            
            System.out.println("随机数据:");
            performanceTest(randomData, "随机");
            
            System.out.println("\n已排序数据:");
            performanceTest(sortedData, "已排序");
            
            System.out.println("\n逆序数据:");
            performanceTest(reversedData, "逆序");
        }
    }
    
    /**
     * 稳定性测试 - 测试算法在各种情况下的表现
     */
    public static void runStabilityTests() {
        System.out.println(BOLD + YELLOW + "\n🎯 稳定性测试" + RESET);
        System.out.println("-" * 30);
        
        // 多次运行相同测试，检查性能稳定性
        int[] testArray = generateRandomArray(10000);
        int runs = 5;
        
        System.out.println("运行 " + runs + " 次相同测试，检查性能稳定性:");
        
        for (int run = 1; run <= runs; run++) {
            System.out.println("\n第 " + run + " 次运行:");
            performanceTest(testArray.clone(), "稳定性测试");
        }
    }
    
    /**
     * 测试所有排序算法的正确性
     */
    private static void testAllAlgorithms(int[] original) {
        if (original.length == 0) {
            System.out.println("  ✅ 空数组测试通过");
            return;
        }
        
        int[] expected = original.clone();
        Arrays.sort(expected);
        
        // 测试双轴快排
        testAlgorithm("双轴快排", original, expected, FastSortingAlgorithms::dualPivotQuickSort);
        
        // 测试并行快排
        testAlgorithm("并行快排", original, expected, FastSortingAlgorithms::parallelQuickSort);
        
        // 测试基数排序（仅正整数）
        if (Arrays.stream(original).allMatch(x -> x >= 0)) {
            testAlgorithm("基数排序", original, expected, FastSortingAlgorithms::radixSort);
        }
        
        // 测试混合排序
        testAlgorithm("混合排序", original, expected, FastSortingAlgorithms::introSort);
        
        // 测试Java内置排序
        testAlgorithm("Java内置", original, expected, Arrays::sort);
    }
    
    /**
     * 测试单个算法
     */
    private static void testAlgorithm(String name, int[] original, int[] expected, 
                                    FastSortingAlgorithms.SortingAlgorithm algorithm) {
        try {
            int[] test = original.clone();
            algorithm.sort(test);
            
            if (Arrays.equals(test, expected)) {
                System.out.println("  " + GREEN + "✅ " + name + " 通过" + RESET);
            } else {
                System.out.println("  " + RED + "❌ " + name + " 失败" + RESET);
                if (original.length <= 20) {
                    System.out.println("    原数组: " + Arrays.toString(original));
                    System.out.println("    期望结果: " + Arrays.toString(expected));
                    System.out.println("    实际结果: " + Arrays.toString(test));
                }
            }
        } catch (Exception e) {
            System.out.println("  " + RED + "❌ " + name + " 异常: " + e.getMessage() + RESET);
        }
    }
    
    /**
     * 性能测试
     */
    private static void performanceTest(int[] data, String dataType) {
        System.out.printf("%-12s | %-10s | %-10s | %-10s | %-10s | %-10s%n", 
                         "算法", "双轴快排", "并行快排", "基数排序", "混合排序", "Java内置");
        System.out.println("-" * 80);
        
        Map<String, Long> results = new HashMap<>();
        
        // 测试双轴快排
        results.put("双轴快排", measureTime(() -> {
            int[] arr = data.clone();
            FastSortingAlgorithms.dualPivotQuickSort(arr);
        }));
        
        // 测试并行快排
        results.put("并行快排", measureTime(() -> {
            int[] arr = data.clone();
            FastSortingAlgorithms.parallelQuickSort(arr);
        }));
        
        // 测试基数排序（仅非负整数）
        if (Arrays.stream(data).allMatch(x -> x >= 0)) {
            results.put("基数排序", measureTime(() -> {
                int[] arr = data.clone();
                FastSortingAlgorithms.radixSort(arr);
            }));
        } else {
            results.put("基数排序", -1L); // 不适用
        }
        
        // 测试混合排序
        results.put("混合排序", measureTime(() -> {
            int[] arr = data.clone();
            FastSortingAlgorithms.introSort(arr);
        }));
        
        // 测试Java内置排序
        results.put("Java内置", measureTime(() -> {
            int[] arr = data.clone();
            Arrays.sort(arr);
        }));
        
        // 输出结果
        System.out.printf("%-12s | ", dataType);
        for (String algorithm : Arrays.asList("双轴快排", "并行快排", "基数排序", "混合排序", "Java内置")) {
            long time = results.get(algorithm);
            if (time == -1) {
                System.out.printf("%-10s | ", "N/A");
            } else {
                System.out.printf("%-10.2f | ", time / 1_000_000.0);
            }
        }
        System.out.println();
        
        // 找出最快的算法
        String fastest = results.entrySet().stream()
                .filter(e -> e.getValue() > 0)
                .min(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("未知");
        
        System.out.println(GREEN + "🏆 最快算法: " + fastest + RESET);
    }
    
    /**
     * 测量执行时间
     */
    private static long measureTime(Runnable task) {
        // 预热
        for (int i = 0; i < 3; i++) {
            task.run();
        }
        
        // 实际测量
        long startTime = System.nanoTime();
        task.run();
        long endTime = System.nanoTime();
        
        return endTime - startTime;
    }
    
    /**
     * 生成随机数组
     */
    private static int[] generateRandomArray(int size) {
        Random random = new Random(42); // 固定种子确保可重复性
        return random.ints(size, 0, size * 10).toArray();
    }
}
