# 常曹 - 技术专家简历

## 基本信息
📞 188-XXXX-XXXX | 📧 <EMAIL> | 📍 上海

---

## 教育背景
**2004.09 - 2007.06**  
常州大学 计算机科学与技术（本科）

---

## 工作经历

### 无锡萨弗特智能科技有限公司 | 研发部门主管（2018.01 - 至今）
- 主导物联网产品规划与软件架构设计
- 负责项目全生命周期管理（需求分析、开发跟踪、资源协调）
- 技术栈：SpringBoot+Dubbo、Netty、Redis、RabbitMQ、Docker

### 金蝶集团 | 技术架构师（2014.05 - 2018.11）
- 主导SOA规范制定与ESB服务总线实施
- 技术栈：金蝶ESB、Oracle、Linux集群、WebLogic

### 中国太平洋保险 | 高级开发工程师（2008.01 - 2014.05）
- 金融级消息中间件架构设计与优化
- 技术栈：IBM WebSphere MQ、C/C++、AIX系统编程

---

## 完整项目经验

### 1. 企业CMMI3认证项目（2018.11-2019.11）
**技术细节**：
- 制定22个标准研发流程文档
- 建立代码审查机制（SonarQube集成）
- 成果：缺陷率降低45%，交付周期缩短30%

### 2. 新零售无人称重货柜物联网项目（在线版 2018.01-2018.11）
**技术架构**：
- 后台：SpringBoot+Dubbo + MyBatis + Redis
- 硬件通信：Netty框架 + 自定义硬件协议
- 部署：Docker虚拟化一键部署
- 关键技术点：
  - Spring AOP实现多数据源切换
  - RabbitMQ削峰填谷
  - 密钥SDK安全加密机制（C语言编译）

### 3. 南昌人社SOA数据集成交换平台（2015.01-2017.05）
**系统组成**：
- 服务总线：金蝶ESB设计器
- 数据同步：Oracle GoldenGate
- 安全体系：数字证书+国密算法
- 管理成果：获评省级数字化改革示范项目

### 4. 金融级消息中间件重构（2011.08-2013.02）
**技术突破**：
- 双活集群架构设计（RTO＜30秒）
- 压力测试工具开发（支撑2000TPS）
- 性能指标：
  - 消息延迟：500ms → 80ms
  - 系统可用性：99.99%

### 5. 跨平台文件传输适配器（2010.04-2011.11）
**技术特性**：
- 支持Windows/Linux/HP-UX/AIX
- TCP/IP多线程传输
- 断点续传机制
- 应用效果：替代FTP传输效率提升80%

---

## 技术能力全景

### 架构设计能力
```mermaid
graph LR
A[物联网架构] --> B(感知层-STM32)
A --> C(传输层-Netty)
A --> D(平台层-SpringCloud)
E[金融中间件] --> F(MQ集群)
E --> G(ESB服务总线)
E --> H(分布式事务)
```

### 技术栈矩阵
| 类别       | 技术组件                                                                 |
|------------|--------------------------------------------------------------------------|
| 开发框架   | SpringBoot/Cloud, Dubbo, MyBatis, Shiro                                  |
| 中间件     | IBM MQ, RabbitMQ, 金蝶ESB, WebLogic                                      |
| 数据库     | Oracle, MySQL, Redis                                                     |
| 系统编程   | AIX/HP-UX系统优化, Shell脚本开发                                         |
| 物联网     | Netty, MQTT协议, 硬件通信协议开发                                        |

---

## 附加信息
**专业认证**：  
- PMP项目管理专家（编号：XXXXXX）
- CISP信息安全认证（编号：XXXXXX）
- 阿里云ACE认证

**技术专利**：  
1. 物联网设备离线通信方法（ZL2020XXXXXX）
2. 金融中间件压力测试系统（ZL2018XXXXXX）

**自我评价**：  
15年全栈技术专家，主导过8个千万级用户量项目落地，擅长复杂系统架构设计与技术团队管理。