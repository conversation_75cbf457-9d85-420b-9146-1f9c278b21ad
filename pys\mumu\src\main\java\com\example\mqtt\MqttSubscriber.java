package com.example.mqtt;

import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MqttSubscriber {
    private static final Logger logger = LoggerFactory.getLogger(MqttSubscriber.class);
    
    private final MqttClient client;
    private final String topic;
    
    public MqttSubscriber(String brokerUrl, String clientId, String topic, String username, String password) throws MqttException {
        this.client = new MqttClient(brokerUrl, clientId, new MemoryPersistence());
        this.topic = topic;
        
        MqttConnectOptions options = new MqttConnectOptions();
        options.setCleanSession(true);
        options.setUserName(username);
        options.setPassword(password.toCharArray());
        options.setAutomaticReconnect(true);
        options.setConnectionTimeout(10);
        client.connect(options);
        logger.info("Connected to broker: {}", brokerUrl);
        
        // 设置消息回调
        client.setCallback(new MqttCallback() {
            @Override
            public void connectionLost(Throwable cause) {
                logger.error("Connection lost", cause);
            }
            
            @Override
            public void messageArrived(String topic, MqttMessage message) {
                String payload = new String(message.getPayload());
                logger.info("Received message on topic {}: {}", topic, payload);
            }
            
            @Override
            public void deliveryComplete(IMqttDeliveryToken token) {
                // 不需要实现
            }
        });
        
        // 订阅主题
        client.subscribe(topic);
        logger.info("Subscribed to topic: {}", topic);
    }
    
    public void disconnect() {
        try {
            if (client != null && client.isConnected()) {
                client.disconnect();
                client.close();
            }
            logger.info("Disconnected from broker");
        } catch (MqttException e) {
            logger.error("Error disconnecting from broker", e);
        }
    }
    
    public static void main(String[] args) {
        String broker = "tcp://localhost:1883";  // MQTT代理地址
        String clientId = "mqtt-subscriber";
        String topic = "bridge/test/#";  // 使用通配符订阅所有测试消息
        String username = "admin";  // MQTT用户名
        String password = "msgbus!QAZ@wsx";  // MQTT密码
        
        try {
            MqttSubscriber subscriber = new MqttSubscriber(broker, clientId, topic, username, password);
            
            // 添加关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(subscriber::disconnect));
            
            logger.info("Subscriber is running. Press Ctrl+C to stop.");
            
            // 保持程序运行
            Thread.currentThread().join();
        } catch (Exception e) {
            logger.error("Error running subscriber", e);
        }
    }
} 