# 🚀 最快排序算法实现与测试

这是一个包含目前最快排序算法的 Java 实现项目，包含完整的测试套件和性能比较。

## 📁 文件结构

```
├── FastSortingAlgorithms.java    # 主要排序算法实现
├── SortingAlgorithmTest.java     # 完整测试套件
├── run_sorting_test.bat          # Windows 运行脚本
├── run_sorting_test.sh           # Linux/Mac 运行脚本
└── README_排序算法.md            # 说明文档
```

## 🏆 包含的排序算法

### 1. 双轴快速排序 (Dual-Pivot Quicksort)
- **Java 标准库使用的算法**
- 比传统快排快 10-15%
- 时间复杂度: O(n log n)
- 适用场景: 通用排序

### 2. 并行快速排序 (Parallel QuickSort)
- 使用 ForkJoin 框架实现
- **多核 CPU 上表现最佳**
- 大数据集性能提升显著
- 适用场景: 大数据集 + 多核处理器

### 3. 基数排序 (Radix Sort)
- **整数排序最快的算法**
- 时间复杂度: O(d×n)，其中 d 是数字位数
- 不基于比较的排序
- 适用场景: 非负整数数组

### 4. 混合排序 (Introsort)
- 结合快排、堆排序、插入排序
- **C++ STL 使用的算法**
- 避免快排的最坏情况
- 适用场景: 需要稳定性能保证

## 🚀 快速开始

### Windows 用户
1. 确保安装了 Java JDK (版本 8 或以上)
2. 双击运行 `run_sorting_test.bat`

### Linux/Mac 用户
1. 确保安装了 Java JDK (版本 8 或以上)
2. 给脚本执行权限: `chmod +x run_sorting_test.sh`
3. 运行脚本: `./run_sorting_test.sh`

### 手动运行
```bash
# 编译
javac FastSortingAlgorithms.java
javac SortingAlgorithmTest.java

# 运行演示
java FastSortingAlgorithms

# 运行完整测试
java SortingAlgorithmTest
```

## 📊 测试内容

### 1. 正确性测试
- 随机数组
- 已排序数组
- 逆序数组
- 相同元素数组
- 重复元素数组
- 不同大小的数组

### 2. 边界测试
- 空数组
- 单元素数组
- 两元素数组
- 大量重复元素

### 3. 性能测试
- 不同大小数组 (1K - 100K)
- 不同数据分布 (随机、已排序、逆序)
- 多次运行平均值
- 算法性能对比

### 4. 稳定性测试
- 多次运行相同测试
- 检查性能稳定性
- 验证算法可靠性

## 📈 性能表现

根据测试结果，不同场景下的最佳选择：

| 场景 | 推荐算法 | 原因 |
|------|----------|------|
| 通用排序 | 双轴快排 | 平衡的性能表现 |
| 大数据集 | 并行快排 | 充分利用多核优势 |
| 整数数组 | 基数排序 | 线性时间复杂度 |
| 稳定性要求 | 混合排序 | 避免最坏情况 |
| 小数组 | 插入排序 | 常数因子小 |

## 💡 使用建议

```java
// 一般情况
FastSortingAlgorithms.dualPivotQuickSort(array);

// 大数据集 + 多核CPU
FastSortingAlgorithms.parallelQuickSort(array);

// 纯整数数组
FastSortingAlgorithms.radixSort(array);

// 需要稳定性能
FastSortingAlgorithms.introSort(array);
```

## 🔧 系统要求

- Java JDK 8 或以上版本
- 支持多核处理器 (并行排序获得最佳性能)
- 内存: 建议 2GB 以上 (大数据集测试)

## 📝 注意事项

1. **基数排序**仅适用于非负整数数组
2. **并行排序**在多核 CPU 上效果更明显
3. 小数组 (< 50 元素) 建议使用插入排序
4. 测试结果可能因硬件配置而异

## 🎯 扩展功能

项目支持以下扩展：
- 添加新的排序算法
- 自定义测试数据
- 调整性能测试参数
- 支持其他数据类型排序

## 📞 技术支持

如果遇到问题或有改进建议，请检查：
1. Java 版本是否正确
2. 编译是否成功
3. 内存是否充足
4. 是否有权限执行脚本

---

**享受高效排序的乐趣！** 🎉
