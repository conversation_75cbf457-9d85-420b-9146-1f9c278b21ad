#include <windows.h>
#include "resource.h"
#include <stdio.h>
#include <ctype.h>
#include <math.h>
#include <time.h>
#include <stdarg.h>
#include "license.h"

// 全局变量
HINSTANCE hInst;
double x, y;
BOOL hasResult = FALSE;
BOOL isCalculating = FALSE;
double radius;  // 存储圆的半径
#define MAX_INPUT_LENGTH 4  // 限制为4位数

// 将所有WriteLog调用注释掉或替换为空操作
#define WriteLog(...)  // 定义为空宏

// 函数声明
BOOL CALLBACK DialogProc(HWND, UINT, WPARAM, LPARAM);
BOOL CalculateCenter(HWND hwnd);

// 添加新的验证函数
BOOL IsValidCoordChar(char ch, const char* currentText)
{
    // 允许删除键
    if (ch == VK_BACK)
        return TRUE;
        
    // 获取当前文本长度
    int len = strlen(currentText);
    
    // 第一个字符可以是负号或数字
    if (len == 0)
        return isdigit(ch);  // 不允许负号，只允许数字
        
    // 如果已经有4位数字，不允许继续输入
    if (len >= MAX_INPUT_LENGTH)
        return FALSE;
    
    // 只允许数字
    return isdigit(ch);
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, 
    LPSTR lpCmdLine, int nCmdShow)
{
    // 初始化许可证
    if (!InitializeLicense()) {
        MessageBoxW(NULL, L"无法初始化许可证", L"错误", MB_ICONERROR);
        return 1;
    }
    
    // 检查许可证
    if (!CheckLicense()) {
        ShowActivationDialog(NULL);
        return 1;
    }
    
    hInst = hInstance;
    DialogBox(hInstance, MAKEINTRESOURCE(IDD_MAIN_DIALOG), NULL, DialogProc);
    return 0;
}

BOOL CALLBACK DialogProc(HWND hwnd, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message)
    {
        case WM_INITDIALOG:
        {
            // 限制每个输入框的文本长度
            for (int i = IDC_EDIT1; i <= IDC_EDIT6; i++) {
                SendDlgItemMessage(hwnd, i, EM_LIMITTEXT, MAX_INPUT_LENGTH, 0);
            }
            // 初始化结果显示
            SetDlgItemTextW(hwnd, IDC_STATIC_RESULT1, L"经度: --");
            SetDlgItemTextW(hwnd, IDC_STATIC_RESULT2, L"纬度: --");
            
            // 显示剩余使用次数
            wchar_t title[100];
            _snwprintf(title, 100, L"抓躺尸 (剩余次数: %d)", GetRemainingUses());
            SetWindowTextW(hwnd, title);
            
            return TRUE;
        }

        case WM_CHAR:
        {
            // 获取当前焦点所在的控件
            HWND hFocus = GetFocus();
            if (hFocus) {
                int id = GetDlgCtrlID(hFocus);
                if (id >= IDC_EDIT1 && id <= IDC_EDIT6) {
                    char ch = (char)wParam;
                    char currentText[MAX_INPUT_LENGTH + 1];
                    GetDlgItemTextA(hwnd, id, currentText, MAX_INPUT_LENGTH);
                    
                    if (!IsValidCoordChar(ch, currentText)) {
                        MessageBeep(MB_ICONERROR);
                        return TRUE;  // 阻止字符输入
                    }
                }
            }
            break;
        }

        case WM_COMMAND:
        {
            switch (LOWORD(wParam))
            {
                case IDC_CALC_BUTTON:
                {
                    if (!CheckLicense()) {
                        ShowActivationDialog(hwnd);
                        return TRUE;
                    }
                    
                    if (!isCalculating) {
                        isCalculating = TRUE;
                        EnableWindow(GetDlgItem(hwnd, IDC_CALC_BUTTON), FALSE);
                        
                        if (!CalculateCenter(hwnd)) {
                            MessageBoxW(hwnd, L"请检查输入数据", L"错误", MB_ICONERROR);
                            // 清除旧的结果
                            SetDlgItemTextW(hwnd, IDC_STATIC_RESULT1, L"经度: --");
                            SetDlgItemTextW(hwnd, IDC_STATIC_RESULT2, L"纬度: --");
                            hasResult = FALSE;
                        } else {
                            // 更新结果显示
                            wchar_t result1[50], result2[50], result3[50];
                            _snwprintf(result1, sizeof(result1)/sizeof(wchar_t), L"圆心经度: %.4f", x);
                            _snwprintf(result2, sizeof(result2)/sizeof(wchar_t), L"圆心纬度: %.4f", y);
                            _snwprintf(result3, sizeof(result3)/sizeof(wchar_t), L"半径: %.4f", radius);
                            SetDlgItemTextW(hwnd, IDC_STATIC_RESULT1, result1);
                            SetDlgItemTextW(hwnd, IDC_STATIC_RESULT2, result2);
                            SetDlgItemTextW(hwnd, IDC_STATIC_RESULT3, result3);
                            // 减少使用次数
                            DecrementUses();
                        }
                        
                        EnableWindow(GetDlgItem(hwnd, IDC_CALC_BUTTON), TRUE);
                        isCalculating = FALSE;
                    }
                    return TRUE;
                }

                case IDC_RESET_BUTTON:
                {
                    // 清空所有输入框
                    for (int i = IDC_EDIT1; i <= IDC_EDIT6; i++) {
                        SetDlgItemTextA(hwnd, i, "");
                    }
                    // 重置结果显示
                    SetDlgItemTextW(hwnd, IDC_STATIC_RESULT1, L"经度: --");
                    SetDlgItemTextW(hwnd, IDC_STATIC_RESULT2, L"纬度: --");
                    hasResult = FALSE;
                    // 将焦点设置到第一个输入框
                    SetFocus(GetDlgItem(hwnd, IDC_EDIT1));
                    return TRUE;
                }

                case IDCANCEL:
                    EndDialog(hwnd, 0);
                    return TRUE;
            }
            break;
        }

        // 移除WM_PAINT处理，因为我们现在使用静态控件显示结果
        default:
            return FALSE;
    }
    return FALSE;
}

BOOL CalculateCenter(HWND hwnd)
{
    char buffer[32];
    double a, b, c, d, e, f;
    double aa, bb, cc, dd, ee, ff;
    
    // 检查输入是否为空
    for (int i = IDC_EDIT1; i <= IDC_EDIT6; i++) {
        if (GetDlgItemTextA(hwnd, i, buffer, sizeof(buffer)) == 0) {
            MessageBoxW(hwnd, L"请输入所有坐标值", L"提示", MB_ICONINFORMATION);
            SetFocus(GetDlgItem(hwnd, i));
            return FALSE;
        }
        
        // 检查是否为有效数字
        for (int j = 0; buffer[j]; j++) {
            if (!isdigit(buffer[j]) && buffer[j] != '.' && buffer[j] != '-') {
                MessageBoxW(hwnd, L"请输入有效的数字", L"错误", MB_ICONERROR);
                SetFocus(GetDlgItem(hwnd, i));
                return FALSE;
            }
        }
    }

    // 获取输入值并检查数值范围
    GetDlgItemTextA(hwnd, IDC_EDIT1, buffer, 32); 
    a = atof(buffer);
    if (!_finite(a) || a > 9999 || a < 0) {  // 限制为0-9999
        MessageBoxW(hwnd, L"输入数值必须在0到9999之间", L"错误", MB_ICONERROR);
        return FALSE;
    }

    GetDlgItemTextA(hwnd, IDC_EDIT2, buffer, 32); 
    b = atof(buffer);
    if (!_finite(b) || b > 9999 || b < 0) {
        MessageBoxW(hwnd, L"输入数值必须在0到9999之间", L"错误", MB_ICONERROR);
        return FALSE;
    }

    GetDlgItemTextA(hwnd, IDC_EDIT3, buffer, 32); 
    c = atof(buffer);
    if (!_finite(c) || c > 1e10 || c < -1e10) {
        MessageBoxW(hwnd, L"输入数值超出范围", L"错误", MB_ICONERROR);
        return FALSE;
    }

    GetDlgItemTextA(hwnd, IDC_EDIT4, buffer, 32); 
    d = atof(buffer);
    if (!_finite(d) || d > 1e10 || d < -1e10) {
        MessageBoxW(hwnd, L"输入数值超出范围", L"错误", MB_ICONERROR);
        return FALSE;
    }

    GetDlgItemTextA(hwnd, IDC_EDIT5, buffer, 32); 
    e = atof(buffer);
    if (!_finite(e) || e > 1e10 || e < -1e10) {
        MessageBoxW(hwnd, L"输入数值超出范围", L"错误", MB_ICONERROR);
        return FALSE;
    }

    GetDlgItemTextA(hwnd, IDC_EDIT6, buffer, 32); 
    f = atof(buffer);
    if (!_finite(f) || f > 1e10 || f < -1e10) {
        MessageBoxW(hwnd, L"输入数值超出范围", L"错误", MB_ICONERROR);
        return FALSE;
    }

    // 检查三点是否重合
    if ((fabs(a - c) < 1e-10 && fabs(b - d) < 1e-10) ||
        (fabs(a - e) < 1e-10 && fabs(b - f) < 1e-10) ||
        (fabs(c - e) < 1e-10 && fabs(d - f) < 1e-10)) {
        MessageBoxW(hwnd, L"输入的点不能重合", L"错误", MB_ICONERROR);
        return FALSE;
    }

    // 计算前检查除数是否为0
    aa = a - c;
    bb = b - d;
    cc = a - e;
    dd = b - f;
    ee = (a * a - c * c + b * b - d * d) / 2;
    ff = (a * a - e * e + b * b - f * f) / 2;

    double denominator = bb * cc - aa * dd;
    if (fabs(denominator) < 1e-10) {
        MessageBoxW(hwnd, L"三点不能在同一直线上", L"错误", MB_ICONERROR);
        return FALSE;
    }

    // 计算结果
    x = (bb * ff - dd * ee) / denominator;
    y = (cc * ee - aa * ff) / denominator;

    // 检查结果是否有效
    if (!_finite(x) || !_finite(y)) {
        MessageBoxW(hwnd, L"计算结果无效，请检查输入", L"错误", MB_ICONERROR);
        return FALSE;
    }

    // 计算半径（到任意一点的距离）
    radius = sqrt((x - a) * (x - a) + (y - b) * (y - b));

    hasResult = TRUE;
    InvalidateRect(hwnd, NULL, TRUE);
    return TRUE;
}

BOOL CheckLicense(void)
{
    LICENSE_DATA licenseData;
    char currentMachineId[MACHINE_ID_LENGTH];
    
    if(!LoadLicenseData(&licenseData)) {
        GetMachineID(currentMachineId);
        ShowActivationDialog(NULL);
        return FALSE;
    }
    
    // Verify machine ID
    GetMachineID(currentMachineId);
    if(strcmp(licenseData.machineId, currentMachineId) != 0) {
        ShowActivationDialog(NULL);
        return FALSE;
    }
    
    // Check expiry
    if(time(NULL) > licenseData.expiryDate) {
        ShowActivationDialog(NULL);
        return FALSE;
    }
    
    // Check uses
    if(licenseData.maxUses <= 0) {
        ShowActivationDialog(NULL);
        return FALSE;
    }
    
    return TRUE;
}

BOOL DecrementUses(void)
{
    LICENSE_DATA licenseData;
    if(!LoadLicenseData(&licenseData)) return FALSE;
    
    licenseData.maxUses--;
    licenseData.checksum = CalculateChecksum(&licenseData);
    
    return SaveLicenseData(&licenseData);
}