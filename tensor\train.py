import os
import torch
import torch.utils.data
from torch.utils.data import Dataset, DataLoader
import torchvision
from torchvision.models.detection import fasterrcnn_resnet50_fpn
from torchvision.models.detection.faster_rcnn import FastRCNNPredictor
import transforms as T
from PIL import Image
import numpy as np
from config import Config
import logging
from datetime import datetime

class GarbageDataset(Dataset):
    def __init__(self, root, transforms=None):
        self.root = root
        self.transforms = transforms
        # 加载所有图像文件，假设图像在 root/images 目录下
        self.imgs = list(sorted(os.listdir(os.path.join(root, "images"))))
        # 加载所有标注文件，假设标注在 root/annotations 目录下
        self.annotations = list(sorted(os.listdir(os.path.join(root, "annotations"))))

    def __getitem__(self, idx):
        # 加载图像和标注
        img_path = os.path.join(self.root, "images", self.imgs[idx])
        ann_path = os.path.join(self.root, "annotations", self.annotations[idx])
        
        img = Image.open(img_path).convert("RGB")
        
        # 加载标注（这里需要根据您的标注格式进行修改）
        boxes = []
        labels = []
        with open(ann_path, 'r') as f:
            for line in f:
                # 假设标注格式为：class_id x_min y_min x_max y_max
                values = list(map(float, line.strip().split()))
                labels.append(int(values[0]))
                boxes.append(values[1:])
        
        boxes = torch.as_tensor(boxes, dtype=torch.float32)
        labels = torch.as_tensor(labels, dtype=torch.int64)
        
        target = {}
        target["boxes"] = boxes
        target["labels"] = labels
        
        if self.transforms is not None:
            img, target = self.transforms(img, target)

        return img, target

    def __len__(self):
        return len(self.imgs)

def get_transform(train):
    transforms = []
    # 转换为张量
    transforms.append(T.ToTensor())
    if train:
        # 训练时的数据增强
        transforms.append(T.RandomHorizontalFlip(0.5))
        transforms.append(T.RandomIoUCrop())
        transforms.append(T.RandomZoomOut())
        transforms.append(T.RandomPhotometricDistort())
    return T.Compose(transforms)

def get_model(num_classes):
    # 加载预训练模型
    model = fasterrcnn_resnet50_fpn(pretrained=True)
    
    # 获取分类器的输入特征数
    in_features = model.roi_heads.box_predictor.cls_score.in_features
    
    # 替换预训练模型的头部
    model.roi_heads.box_predictor = FastRCNNPredictor(in_features, num_classes)
    
    return model

def train_one_epoch(model, optimizer, data_loader, device, epoch):
    model.train()
    total_loss = 0
    
    for i, (images, targets) in enumerate(data_loader):
        images = list(image.to(device) for image in images)
        targets = [{k: v.to(device) for k, v in t.items()} for t in targets]

        loss_dict = model(images, targets)
        losses = sum(loss for loss in loss_dict.values())
        
        optimizer.zero_grad()
        losses.backward()
        optimizer.step()
        
        total_loss += losses.item()
        
        if i % 50 == 0:
            print(f"Epoch: {epoch}, Batch: {i}, Loss: {losses.item():.4f}")
    
    return total_loss / len(data_loader)

def evaluate(model, data_loader, device):
    model.eval()
    total_loss = 0
    
    with torch.no_grad():
        for images, targets in data_loader:
            images = list(image.to(device) for image in images)
            targets = [{k: v.to(device) for k, v in t.items()} for t in targets]
            
            loss_dict = model(images, targets)
            losses = sum(loss for loss in loss_dict.values())
            total_loss += losses.item()
    
    return total_loss / len(data_loader)

def main():
    # 设置设备
    device = torch.device(Config.DEVICE if torch.cuda.is_available() else "cpu")
    
    # 设置数据集路径
    data_path = "dataset"
    
    # 创建数据集
    dataset = GarbageDataset(
        os.path.join(data_path, "train"),
        get_transform(train=True)
    )
    dataset_test = GarbageDataset(
        os.path.join(data_path, "val"),
        get_transform(train=False)
    )

    # 创建数据加载器
    data_loader = DataLoader(
        dataset, batch_size=2, shuffle=True,
        collate_fn=lambda x: tuple(zip(*x))
    )
    data_loader_test = DataLoader(
        dataset_test, batch_size=1, shuffle=False,
        collate_fn=lambda x: tuple(zip(*x))
    )

    # 获取模型
    num_classes = len(Config.GARBAGE_CLASSES) + 1  # +1 表示背景类
    model = get_model(num_classes)
    model.to(device)

    # 设置优化器
    params = [p for p in model.parameters() if p.requires_grad]
    optimizer = torch.optim.SGD(params, lr=0.005, momentum=0.9, weight_decay=0.0005)
    
    # 设置学习率调度器
    lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=3, gamma=0.1)

    # 训练循环
    num_epochs = 10
    best_loss = float('inf')
    
    for epoch in range(num_epochs):
        # 训练一个周期
        train_loss = train_one_epoch(model, optimizer, data_loader, device, epoch)
        
        # 更新学习率
        lr_scheduler.step()
        
        # 评估
        val_loss = evaluate(model, data_loader_test, device)
        
        print(f"Epoch {epoch} - Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
        
        # 保存最佳模型
        if val_loss < best_loss:
            best_loss = val_loss
            torch.save(model.state_dict(), Config.MODEL_PATH)
            print(f"Saved best model with validation loss: {val_loss:.4f}")

if __name__ == "__main__":
    main() 