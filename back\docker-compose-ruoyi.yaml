version: '3.8'
services:
  ruoyi-mysql:
    image: mysql:5.7
    container_name: ruoyi-mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
    ports:
      - "3305:3306"
    volumes:
      - ruoyi-mysql-data:/var/lib/mysql
      - ./db_init:/docker-entrypoint-initdb.d
    # restart: always  # 暂时移除

  ruoyi-redis:
    image: redis:latest
    container_name: ruoyi-redis
    ports:
      - "6378:6379"
    volumes:
      - ruoyi-redis-data:/data
    # restart: always # 暂时移除

  ruoyi-backend:
    image: rouyibackend:latest
    container_name: ruoyi-backend
    environment:
      - SPRING_DATASOURCE_URL=************************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=${DB_USER}
      - SPRING_DATASOURCE_PASSWORD=${DB_PASSWORD}
      - SPRING_REDIS_HOST=${REDIS_HOST}
      - SPRING_REDIS_PORT=${REDIS_PORT}
    ports:
      - "8077:8899"
    depends_on:
      - ruoyi-mysql
      - ruoyi-redis
    # restart: always # 暂时移除

  ruoyi-frontend:
    image: rouyifrontend:latest
    container_name: ruoyi-frontend
    ports:
      - "77:80"
    depends_on:
      - ruoyi-backend
    # restart: always # 暂时移除

volumes:
  ruoyi-mysql-data:
  ruoyi-redis-data: