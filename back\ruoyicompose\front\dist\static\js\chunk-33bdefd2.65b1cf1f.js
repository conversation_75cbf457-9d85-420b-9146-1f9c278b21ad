(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-33bdefd2","chunk-0a74ec40"],{"2b60":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),a("el-form-item",{attrs:{label:"条码",prop:"code"}},[a("el-input",{attrs:{placeholder:"请输入条码",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.code,callback:function(t){e.$set(e.queryParams,"code",t)},expression:"queryParams.code"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.common_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:fixed:add"],expression:"['asset:fixed:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:fixed:export"],expression:"['asset:fixed:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.fixedList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"名称",align:"center",prop:"name"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.common_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"图片",align:"center",prop:"picData",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("image-preview",{attrs:{src:e.row.picData,width:50,height:50}})]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),a("el-table-column",{attrs:{label:"添加时间",align:"center",prop:"createTime"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:info:edit"],expression:"['asset:info:edit']"}],staticStyle:{color:"#55ed27"},attrs:{size:"mini",type:"text",icon:"el-icon-help"},on:{click:function(a){return e.handleItem(t.row)}}},[e._v("明细 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:fixed:edit"],expression:"['asset:fixed:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),"1"==t.row.status?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:fixed:remove"],expression:"['asset:fixed:remove']"}],staticStyle:{color:"#ff0000"},attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")]):e._e()]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入名称",maxlength:"50","show-word-limit":""},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.common_status,(function(t){return a("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label)+" ")])})),1)],1),a("el-form-item",{attrs:{label:"图片",prop:"picData"}},[a("image-upload",{attrs:{limit:1},model:{value:e.form.picData,callback:function(t){e.$set(e.form,"picData",t)},expression:"form.picData"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入内容",maxlength:"200","show-word-limit":""},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("fixed-item",{ref:"assetFixedItemRef"})],1)},n=[],i=a("5530"),l=(a("d81d"),a("f39a")),s=a("d3c2"),o={name:"Fixed",components:{FixedItem:s["default"]},dicts:["common_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,fixedList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,parentId:null,deptId:null,name:null,code:null,type:"0",orderNum:null,status:null,picData:null},form:{},rules:{parentId:[{required:!0,message:"父级id不能为空",trigger:"blur"}],deptId:[{required:!0,message:"部门不能为空",trigger:"blur"}],name:[{required:!0,message:"名称不能为空",trigger:"blur"}],type:[{required:!0,message:"类型不能为空",trigger:"change"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}],orderNum:[{required:!0,message:"顺序不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(l["d"])(this.queryParams).then((function(t){e.fixedList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,parentId:null,deptId:null,name:null,code:null,type:"0",orderNum:1,status:"0",picData:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加固定资产"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;Object(l["c"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改固定资产"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?Object(l["e"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(l["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除固定资产编号为"'+a+'"的数据项？').then((function(){return Object(l["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("asset/fixed/export",Object(i["a"])({},this.queryParams),"fixed_".concat((new Date).getTime(),".xlsx"))},handleItem:function(e){this.$refs["assetFixedItemRef"].openDialog(e)}}},u=o,c=a("2877"),m=Object(c["a"])(u,r,n,!1,null,null,null);t["default"]=m.exports},d3c2:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.itemTitle,visible:e.itemOpen,fullscreen:"",width:"100%","append-to-body":""},on:{"update:visible":function(t){e.itemOpen=t},close:e.closeItem}},[a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),a("el-form-item",{attrs:{label:"条码",prop:"code"}},[a("el-input",{attrs:{placeholder:"请输入条码",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.code,callback:function(t){e.$set(e.queryParams,"code",t)},expression:"queryParams.code"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.common_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:fixed:export"],expression:"['asset:fixed:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"danger",plain:"",icon:"el-icon-close",size:"mini"},on:{click:e.closeItem}},[e._v("关闭 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.fixedList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"名称",align:"center",prop:"name"}}),a("el-table-column",{attrs:{label:"条码",align:"center",prop:"code"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.common_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入名称",maxlength:"50","show-word-limit":""},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"条码",prop:"code"}},[a("el-input",{attrs:{placeholder:"请输入条码",maxlength:"50","show-word-limit":""},model:{value:e.form.code,callback:function(t){e.$set(e.form,"code",t)},expression:"form.code"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.common_status,(function(t){return a("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label)+" ")])})),1)],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入内容",maxlength:"200","show-word-limit":""},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)])},n=[],i=a("5530"),l=(a("d81d"),a("b0c0"),a("f39a")),s={name:"FixedItem",dicts:["common_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,fixedList:[],title:"",itemTitle:"物资明细",itemOpen:!1,selectedAsset:void 0,open:!1,queryParams:{pageNum:1,pageSize:10,parentId:null,deptId:null,name:null,code:null,type:"1",orderNum:null,status:null,picData:null},form:{},rules:{parentId:[{required:!0,message:"父级id不能为空",trigger:"blur"}],deptId:[{required:!0,message:"部门不能为空",trigger:"blur"}],name:[{required:!0,message:"名称不能为空",trigger:"blur"}],code:[{required:!0,message:"条码不能为空",trigger:"blur"}],type:[{required:!0,message:"类型不能为空",trigger:"change"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}],orderNum:[{required:!0,message:"顺序不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(l["d"])(this.queryParams).then((function(t){e.fixedList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,parentId:null,deptId:null,name:null,code:null,type:"1",orderNum:1,status:"0",picData:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.selectedAsset&&(this.form.parentId=this.selectedAsset.id),this.open=!0,this.title="添加固定资产"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;Object(l["c"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改固定资产"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?Object(l["e"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(l["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除固定资产编号为"'+a+'"的数据项？').then((function(){return Object(l["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("asset/fixed/export",Object(i["a"])({},this.queryParams),"fixed_".concat((new Date).getTime(),".xlsx"))},openDialog:function(e){this.selectedAsset=e,this.itemOpen=!0,this.queryParams.parentId=e.id,this.itemTitle=e.name+"--物资明细",this.resetQuery()},closeItem:function(){this.itemOpen=!1}}},o=s,u=a("2877"),c=Object(u["a"])(o,r,n,!1,null,null,null);t["default"]=c.exports},f39a:function(e,t,a){"use strict";a.d(t,"d",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return l})),a.d(t,"e",(function(){return s})),a.d(t,"b",(function(){return o}));var r=a("b775");function n(e){return Object(r["a"])({url:"/asset/fixed/list",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/asset/fixed/"+e,method:"get"})}function l(e){return Object(r["a"])({url:"/asset/fixed",method:"post",data:e})}function s(e){return Object(r["a"])({url:"/asset/fixed",method:"put",data:e})}function o(e){return Object(r["a"])({url:"/asset/fixed/"+e,method:"delete"})}}}]);