教育背景
200409-2007.06
工作经验
201801-至今
常州大学学院
计算机科学与技术(本科)
无锡萨弗特智能科技有限公司部门管理，技术牵头。
研发部门主管
物联网产品规划，设计，软件架构。
项目需求分析，管理开发跟踪。
外部项目沟通，资源利用。
201405-201811
2007.09-201405
金蝶集团
项目管理技术架构
负责软件需求调研、项目任务书、软件需求规格说明书的编写及确认。技术框架选型，原型设计讨论确认，技术疑问攻关。负责软件项目整体实施的计划的编制、核实、确认和发布，跟踪与监控软件项目的实施进度，确保项目按计划执行。完成整个项目软件部分以及与硬件接口部分的实施并确保项目的最终验收。负责项目履行过程中与用户，供应商，集成商，公司其它相关部门关于公司自主产品技术问题和管理问题的协调和沟通。
中国太平洋保险公司外派项目管理团队管理
软件开发
项目管理技术开发
项目经验
201811-至今
企业 CMMI3 评选项目
项目总负责
描述:
CMMI3 认证意义:企业能够对项目的实施有一整套的管理措施，并保障项目的完成;企业能够根据自身的特殊情况以及自己的标准流程，将这套管理体系与流程予以制度化不仅能够在同类的项目上得到成功的实施，在不同类的项目上一样能够得到成功的实施。科学的管理成为企业的一种文化，企业的组织财富。公司决定在 2019 年上半年通过 CMMI3 认证。责任:
1/5
负责整个项目的定义工作安排，培训接洽，人员组织，技术人员帅选。
负责参评项目选型，项目文档规范构建补充。
负责参评现场的统筹安排，制定过CMMI3 的具体流程。
201809-201811
新零售无人称重货柜物联网项目(离线版)描述:
项目管理技术架构
无人智能货柜离线版为无人智能货柜在线版的客户定制化需求补充。为了满足客户无电要求而定制化改造的项目。主要改造点在于结合手机 APP 二维码扫码技术以及密钥 SDK 比对技术对在线版本进行改造。单片机部分实行 2G 通信模块移除，加入二维码生成算法以及密钥生成算法，责任:
项目管理，需求制定，进度跟踪等等
201801-201811
项目管理技术架构
新零售无人称重货柜物联网项目(在线版)描述:
无人智能货柜是依托物联网技术而兴起的新零售模式,而无人智能货柜平台将实现无人智能货柜线上线下智能管理，数据分析，业务监控等，实现智能化新零售。依托物联网实现智能化购物，无人化管理。智能终端通过各种传感器收集用户购物行为进行，如称重数据，报价预判，关门等状态收集。移动端以小程序技术开发，用于客户选购商品，动态展示商品增减，商品支付，报警跟踪等等。后台技术架构主要为 springboot+dubbo 实现商家管理，预售商品登记，支付设置，支付提现，信息统计图表等等。硬件通信采用 netty 框架，基于自定义的硬件协议配合密钥 SDK(ini 编译 C 语言函数)机制进行安全加密实现硬件与后台的通信。技术栈:
Maven 管理构建项目。thymeleaf、bootstrap、layer 等框架完成前端展示功能MyBatis Druid 等持久层框架。
百度 webuploader、EChars 完成文件上传和图片展示quatarz 完成消费者未支付订单及提醒功能及其他系统调度任务，采用redis 缓存技术提高系统效率
Shiro 完成数据权限信息管理。
依托 springAOP 技术实现多数据源切换。Rabbit MQ 队列技术实现发布订阅，减少远程 RPC 压力，优化性能。交付部署采用 docker 虚拟化方式一键配置部署服务，实现灵活话部署,责任:
从项目立项到项目技术架构再到项目进度成本跟踪最后项目测试交付的一条龙管理不限于项目的框架搭建，技术选型，疑难攻关已经后期性能优化。
2015.01-2017.05
南昌人社 SOA 数据集成交换平台描述:
项目管理技术架构
1，南昌人社局信息中心的系统安全、性能、软硬件配置等整体规划，在软硬件、网络等基础设施方面已经具备了一套比较全面的从物理层到管理层的完整安全、性能监控体系对南昌人社内现有业务系统进行数据及服务集成，并在统一的集成框架下，补充现有产品中不足的业务功能，及基于中间件产品的二次开发,最终将形成“一网一平台”的基本业务功能框架。
2，技术产品选型金蝶 ESB 设计器,开发工具,eclipse,soapui,开发工具linux，AESB,CXF,oracle，软件环境标准 SOA 规范的制订，统一社保局多个系统间的接口规范!建设 SOA 应用集成平台，完成服务资产目录和服务库管理。3，项目模块划分，指定项目进度周期表，项目进度跟踪，项目质量保证等。4，项目一期完成后参与二期建设工作的讨论等。
2015.01--2017.05
项目管理技术架构
南昌人社 SOA 数据集成交换平台描述:
1，南昌人社局信息中心的系统安全、性能、软硬件配置等整体规划，在软硬件、网络等基础设施方面已经具备了一套比较全面的从物理层到管理层的完整安全、性能监控体系。对南昌人社内现有业务系统进行数据及服务集成，并在统一的集成框架下，补充现有产品中不足的业务功能，及基于中间件产品的二次开发,最终将形成“一网一平台”的基本业务功能框架。
2，技术产品选型金蝶 ESB 设计器,开发工具,eclipse,soapui,开发工具inux，AESB,CXF,oracle，软件环境标准 SOA 规范的制订，统一社保局多个系统间的接口规范!建设 SOA 应用集成平台，完成服务资产目录和服务库管理。3，项目模块划分，指定项目进度周期表，项目进度跟踪，项目质量保证等。4，项目一期完成后参与二期建设工作的讨论等。
215
201601--201608
2013.03201405
上图馆数据集成项目描述:
项目管理技术架构
上海经信委合作项目，设计上图馆现有项目之间信息孤岛打通，采用总线型机构设计。应用金蝶 ESB 服务总线产品对上海图书馆各个系统数据实现集成，通过其提供的各种输入输出及转换组件，从各种输入介质如 Text 文件、XML、ExceI、DBMS 的表中获取源数据进行初始采集和增量采集、处理。企业应用服务注册,服务编排,应用协议转换对多个服务进行编排组合，并根据服务请求方的请求内容来进行消息路由，路由至不同的服务提供方。
上海浦东公交系统财务数据中心描述:
金蝶 ESB 设计器,开发工具
项目管理技术架构
前期业务梳理，数据整理。金蝶 ESB，金蝶 MQ，sqlserver2008 软件环境打破原有上海某公交财务系统各个分部之间的数据孤立状态，成功的使其连成一线，完成数据间的逻辑 ETL操作，促成统一网状架构。
2013.03-201405
201207-2013.02
201204-201207
坐席运营系统
项目管理开发
描述:
系统包括坐席系统部分和运营管理，知识库等部分。坐席系统包括软电话呼叫中心，通用业务，产险业务受理，产险业务查询，产险回访，寿险务受理，寿险业务查询，寿险回访。运营管理包含诸多产寿险管理条目如机构管理，品牌管理，回访管理等等。技术上基于 SPING,WEBWORK,HIBERNATE,中间件基于 was,wmg，数据库基于ORACLE.开发进度上，本阶段属于项目的二期，主要是整合多个运营管理系统。
在线出单系统
项目管理开发
描述:
在线出单系统为太平洋寿险各个分公司时时线上出单系统。拥有涵盖寿险几乎所有险种与诸多系统对接，实现其总打印平台功能。
数据分时时与批量等方式与寿险核心同步，保单生效等，新险种持续对接入改平台从而从分工单打印，同步入总公司集团。
数据交换系统后台管理描述:
项目管理开发
为适应新平台不断增加的接口业务需要,以及旧管理系统不断升高的维护成本而重写此后台管理系统。
基于 SSH框架，前台页面显示基于 EXTJS 框架+JSON+JSP，后台数据库为 oracle,应用服务器为 was。
新系统对于产，寿险通道申请区别处理，增加通道时时维护，监视等功能，解放维护人员大量工作量。
增加相关自选项，减少与业务人员沟通工作量，使得系统更具有人性化，自动化。
2011.08201203
WaSphere ESB 系统持续改造描述:
项目管理开发
WD webshpere intergration developer, ECLiPs 开发工具AIX 硬件环境
websphere esb 软件环境基于产险 ESB 内部消息交互系统改造。包括
1)原有项目程序重新设计，代码重新精简，功能重新规划。
2)监控系统进一步升级，完善。
3)消息路由模块重构，整合。
4)消息解析模块重构，整合。
5)预生产环境压力评估系统开发，
2011.08
2012 03
项目管理开发
WeSphere ESB 系统持续改造
描述:
WD webshpere intergration developer, ECLIPs 开发工具AIX 硬件环境
websphere esb 软件环境基于产险 ESB 内部消息交互系统改造。包括
1)原有项目程序重新设计，代码重新精简，功能重新规划。
315
2)监控系统进一步升级，完善。
3)消息路由模块重构，整合
4)消息解析模块重构，整合,
5)预生产环境压力评估系统开发
2010042011.11
文件数据交互器
描述:
项目管理开发
visual c++，v 开发工具
HP-UX 小型机硬件环境
C 软件环境1为弥补消息交换总线传特大文件上性能的不足的问题,开发用于点对点的文件传输适配器。
2适配器基于C语言，可成功运行于WNDOWS，LINUX，HP-UX，以及 AIX 等操作系统。
3 本模块基于 TCP +P 下的 SOCKET 编程，采用多线程机制，线性日志模式，以及必要的备份功能。
4 重要功能则是可以确保文件分批次发送，采用TRIG 机制。
2011.04 2011.06
数据交互中心监控改造描述:
项目管理开发
v 开发工具
小型机硬件环境
HP-UX，shell 脚本软件环境进一步完善关于消息交换中间件 WMQ 的相关监控系统。针对业务量大的系统，进行通道状态以及队列深度的全天监控，并且与短信系统相关接口对接，以达到发短信监控的目的。
2011.01--2011.05
201003-201011
2009.06-201001
消息交换总线核心架构改造描述:
项目管理开发
visual c++，v 开发工具HP-UX 小型机硬件环境C 软件环境为适应新的不断增加的消息信息量，解决原有热备单工方式存在过多资源浪费，处理能比达到瓶颈等问题，现要求以中心集群方式重新部署整个 SOA 中心系统，实现 MB 双工，MQ 实现集群，前置通讯队列管理器热备的架构。
企业级数据交互功能(PUBSUB)描述:
项目开发
MyEclipse，visual c++，v 开发工具HP-UX 小型机硬件环境
JAVA,C 软件环境 2010.3-2010.11
1 为适应新需求，已经充分发挥 MB 的发布订阅功能，需要开发新的适配器接口,并且改版本拥有 C,java 两种版本。
2 模块功能分别采用 JAVA 以及 C 语言编写，以适应不同的请求方或者响应方。3 模块依赖于 websphere MB 中心代理进行主题树的生成与管理，控制流队列对于代理的控制而保持其发布订阅功能。4编写适配器，增加 PUBSUB 接口，实现 MQ API，进而达到能够操作中心主题数，发布主题，订阅主题。
消息交互总线支持功能增加(TUXEDO)描述:
vi，visual c++开发工具
HP-UX 小型机硬件环境
c 语言，HP-UX 软件环境
开发
1为适消息响应方方的 TUXEDO 服务，为消息交换系统增加 TUXEDO 调用客户端。
2 该模块是在 HP-UX 操作系统下基于 websphere MQ api 采用C语言编写。
3 具体功能负责从 MQ 队列中取走消息并且调用 TUX 服务。
4 实现中采用多进程线性日志编写法。
5 后期测试以及功能修改维护等等。
200808-2009.05
200801--200807
技能特长
自我评价
企业消息交换总线(IBM SOA)描述:
开发
MEclipse 开发工具HP-UX 小型机硬件环境
JAVA 软件环境1该项目是基于|BM websphere message queue,lBM webspheremessage broker 的一套企业级别消息交换框架。其中包含 MB 中的 ESQL 编程,webshpere mg 的配置以及 webshpere mg 相关 AP| 知识。2 该模块主要完成基于 MQ 的 API 下的基于 HTTP 协议的 webservice 调用。3 模块要实现多线程处理已经比较灵活的异常处理，详细的日志记录功能。4 开发后期需要进行功能测试以及压力测试。
企业运营信息汇集系统描述:MyEclipse 开发工具
开发
PC 服务器硬件环境
i2ee，unix 软件环境1该项目是为电信运营的P 记费服务的。该系统运行于 Tomcat 平台,采用 J2EE 架构、Struts 框架，Hibernate,Spring 技术及 Oracle 9i 数据库系统。该系统主要分为用户自服务管理，管理员管理，超级管理员管理等模块。2此系统后台实现的功能是定期采集原始计费日志文件，并将采集的数据封装整理数据清单，然后通过 Socket 上传给中央处理系统;中央处理系统收集信息并通过 Pro*C 将数据保存到 Orace 数据库当前时间所对应的 hour_x 表中，最后利用 PUSQL 周期性进行数据的整合。3 在本系统中通过 Socket与 Unix 服务器进行通讯,以实现 Unix服务器与数据库信息同步。
精通java 语言开发 ava web ssm，springboot 框架开发，熱练使用 springcoud 分布式框架搭建开发。熟练使用 IBM esb，messagebroker,金蝶 esb 进行 SOA 定制化开发，熟悉 weblogicwebsphere 等安装部署，熟悉国产金蝶中间件，了解其性能优化。熟悉物联网技术开发，以及使用 netty 框架进行后台与单片机通信开发。熟悉基于java 的公众号以及小程序开发，了解 androd 开发。熟练使用多种 MQ，诸如ibm wmmg，activimg，rabbitmg 等熟练使用 linux，aix，hp-ux 等。熟练使用 shell 命令，编程。熟练使用 oracle，mysql 等数据库熟练使用多种 etl 同步工具进行数据同步开发，诸如kettle，金蝶el等。
超过 10 年项目及团队管理经验，需求分析，方案设计经验。资深 SOA 架构师，系统集成师，项目管理。曾在中国太平洋保险公司研发部从事基于IBM 产品消息总线项目达7年。一年多的物联网企业项目管理经验，熟悉了创业型企业的基本模式，了解到了中国创业型企业的竞争环境。