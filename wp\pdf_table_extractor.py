import pandas as pd
import pdfplumber
import openpyxl
from openpyxl import Workbook
import os

def extract_tables_from_pdf(pdf_path, excel_path):
    # Open the PDF file
    with pdfplumber.open(pdf_path) as pdf:
        # Create a new Excel workbook
        wb = Workbook()
        # Remove the default sheet
        wb.remove(wb.active)
        
        # Process each page
        for i, page in enumerate(pdf.pages):
            # Extract tables from the page
            tables = page.extract_tables()
            
            if tables:
                # Create a sheet for this page
                sheet_name = f"Page_{i+1}"
                sheet = wb.create_sheet(title=sheet_name)
                
                # Process each table on the page
                for table_idx, table in enumerate(tables):
                    # Convert table to DataFrame
                    df = pd.DataFrame(table)
                    
                    # Write the table to the sheet with some spacing between tables
                    start_row = table_idx * (len(df) + 3) + 1
                    
                    # Add table number as a header
                    sheet.cell(row=start_row, column=1, value=f"Table {table_idx+1}")
                    
                    # Write the data
                    for r_idx, row in enumerate(df.values):
                        for c_idx, value in enumerate(row):
                            sheet.cell(row=start_row+r_idx+1, column=c_idx+1, value=value)
            else:
                # Create an empty sheet for pages without tables
                sheet_name = f"Page_{i+1}"
                sheet = wb.create_sheet(title=sheet_name)
                sheet.cell(row=1, column=1, value="No tables found on this page")
        
        # Save the workbook
        wb.save(excel_path)
        print(f"Tables extracted and saved to {excel_path}")

if __name__ == "__main__":
    # Get user input for file paths
    pdf_path = input("Enter the path to your PDF file: ")
    excel_path = input("Enter the path for the output Excel file: ")
    
    # Ensure the excel file has the correct extension
    if not excel_path.endswith('.xlsx'):
        excel_path += '.xlsx'
    
    # Extract tables
    extract_tables_from_pdf(pdf_path, excel_path) 