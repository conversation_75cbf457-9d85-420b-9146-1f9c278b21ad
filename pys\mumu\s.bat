@echo off

rem jar平级目录
set AppName=v.jar

rem 设置jar文件的完整路径
set JAR_PATH=%~dp0%AppName%

rem 切换到脚本所在目录
cd /d %~dp0

rem 设置dump文件输出目录
set DUMP_PATH=%~dp0dump

rem 创建dump目录（如果不存在）
if not exist %DUMP_PATH% mkdir %DUMP_PATH%

rem JVM参数
set JVM_OPTS=-Dname=%AppName% -Duser.timezone=Asia/Shanghai -Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m
set JVM_OPTS=%JVM_OPTS% -XX:+HeapDumpOnOutOfMemoryError
set JVM_OPTS=%JVM_OPTS% -XX:HeapDumpPath="%DUMP_PATH%\heap_dump_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.hprof"
set JVM_OPTS=%JVM_OPTS% -XX:ErrorFile="%DUMP_PATH%\hs_err_pid%%p.log"
set JVM_OPTS=%JVM_OPTS% -Xloggc:"%DUMP_PATH%\gc_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.log"
set JVM_OPTS=%JVM_OPTS% -XX:+PrintGCDateStamps -XX:+PrintGCDetails -XX:NewRatio=1 -XX:SurvivorRatio=30 -XX:+UseParallelGC -XX:+UseParallelOldGC

:menu
ECHO.
	ECHO.  [1] 启动%AppName%
	ECHO.  [2] 关闭%AppName%
	ECHO.  [3] 重启%AppName%
	ECHO.  [4] 启动状态 %AppName%
	ECHO.  [5] 退 出
ECHO.

ECHO.请输入选择项目的序号:
set /p ID=
	IF "%id%"=="1" GOTO start
	IF "%id%"=="2" GOTO stop
	IF "%id%"=="3" GOTO restart
	IF "%id%"=="4" GOTO status
	IF "%id%"=="5" EXIT
goto menu

:start
    rem 显示当前环境信息
    echo Current Directory: %CD%
    echo JAR Path: %JAR_PATH%
    echo DUMP Path: %DUMP_PATH%
    
    rem 检查jar文件是否存在
    if not exist "%JAR_PATH%" (
        echo Error: Cannot find %AppName% at: %JAR_PATH%
        pause
        goto menu
    )

    for /f "usebackq tokens=1-2" %%a in (`jps -l ^| findstr %AppName%`) do (
        set pid=%%a
        set image_name=%%b
    )
    if defined pid (
        echo %AppName% is already running
        pause
        goto menu
    )

    echo Starting Java process with following parameters:
    echo JVM_OPTS: %JVM_OPTS%
    start javaw %JVM_OPTS% -jar v.jar
    echo  starting %AppName%......
    rem 等待几秒让程序启动
    timeout /t 3 >nul
    
    rem 验证程序是否成功启动
    for /f "usebackq tokens=1-2" %%a in (`jps -l ^| findstr %AppName%`) do (
        set pid=%%a
        set image_name=%%b
    )
    if not defined pid (
        echo Failed to start %AppName%
        echo Please check if the jar file exists and Java environment is correct
    ) else (
        echo Start %AppName% success... PID: %pid%
    )
    pause
    goto menu

rem 函数stop通过jps命令查找pid并结束进程
:stop
	for /f "usebackq tokens=1-2" %%a in (`jps -l ^| findstr %AppName%`) do (
		set pid=%%a
		set image_name=%%b
	)
	if not defined pid (echo process %AppName% does not exists) else (
		echo prepare to kill %image_name%
		echo start kill %pid% ...
		rem 根据进程ID，kill进程
		taskkill /f /pid %pid%
	)
	pause
	goto menu

:restart
	call :stop
    call :start
	goto menu

:status
	for /f "usebackq tokens=1-2" %%a in (`jps -l ^| findstr %AppName%`) do (
		set pid=%%a
		set image_name=%%b
	)
	if not defined pid (echo process %AppName% is dead ) else (
		echo %image_name% is running
	)
	pause
	goto menu
