# 使用 OpenJDK 8 作为基础镜像
FROM openjdk:8-jdk-alpine

# 设置工作目录
WORKDIR /app

# 安装网络工具和 MySQL 客户端
RUN apk add --no-cache netcat-openbsd curl mysql-client iputils

# 将 Maven 构建的 JAR 文件复制到容器中
COPY back/cdaid.jar app.jar

# 创建配置目录
RUN mkdir -p /app/config


# 设置环境变量
ENV JAVA_OPTS="-Dname=cdaid.jar -Dfile.encoding=UTF-8 -Duser.timezone=Asia/Shanghai -Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m -XX:+HeapDumpOnOutOfMemoryError"

# 暴露端口
EXPOSE 8899

# 定义启动命令
CMD ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
