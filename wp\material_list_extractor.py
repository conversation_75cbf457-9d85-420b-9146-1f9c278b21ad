import fitz
import pandas as pd
import re
import os
import pyautogui
import time
import random

# 尝试导入OCR相关模块，如果不可用则跳过
try:
    import cv2
    import numpy as np
    from PIL import Image
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    print("警告: OCR相关模块未安装，将仅使用PyMuPDF进行表格识别")

def extract_image_from_pdf(page, zoom=4):  # 增加缩放比例以提高清晰度
    """从PDF页面提取图像用于OCR处理"""
    pix = page.get_pixmap(matrix=fitz.Matrix(zoom, zoom))
    img_data = pix.samples
    img = Image.frombytes("RGB", [pix.width, pix.height], img_data)
    return np.array(img)

def detect_grid_table(img):
    """检测网格线表格"""
    if not OCR_AVAILABLE:
        return None
    
    try:
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 二值化处理
        _, binary = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY_INV)
        
        # 使用形态学操作增强线条
        kernel = np.ones((3, 3), np.uint8)
        dilated = cv2.dilate(binary, kernel, iterations=1)
        
        # 检测水平和垂直线
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
        horizontal_lines = cv2.morphologyEx(dilated, cv2.MORPH_OPEN, horizontal_kernel)
        
        vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 40))
        vertical_lines = cv2.morphologyEx(dilated, cv2.MORPH_OPEN, vertical_kernel)
        
        # 合并水平和垂直线
        grid_lines = cv2.add(horizontal_lines, vertical_lines)
        
        # 查找轮廓
        contours, _ = cv2.findContours(grid_lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 如果找到足够多的轮廓，认为存在网格表格
        if len(contours) > 10:
            return True
        
        return False
    except Exception as e:
        print(f"检测网格表格时出错: {str(e)}")
        return False

def extract_grid_table(img):
    """提取网格线表格内容"""
    if not OCR_AVAILABLE:
        return None
    
    try:
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 二值化处理
        _, binary = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY_INV)
        
        # 使用形态学操作增强线条
        kernel = np.ones((3, 3), np.uint8)
        dilated = cv2.dilate(binary, kernel, iterations=1)
        
        # 检测水平和垂直线
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
        horizontal_lines = cv2.morphologyEx(dilated, cv2.MORPH_OPEN, horizontal_kernel)
        
        vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 40))
        vertical_lines = cv2.morphologyEx(dilated, cv2.MORPH_OPEN, vertical_kernel)
        
        # 合并水平和垂直线
        grid_lines = cv2.add(horizontal_lines, vertical_lines)
        
        # 查找轮廓
        contours, _ = cv2.findContours(grid_lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 按照y坐标排序轮廓（从上到下）
        sorted_contours = sorted(contours, key=lambda c: cv2.boundingRect(c)[1])
        
        # 提取每个单元格的内容
        cells_data = []
        for contour in sorted_contours:
            x, y, w, h = cv2.boundingRect(contour)
            
            # 忽略太小的轮廓
            if w < 20 or h < 20:
                continue
            
            # 提取单元格区域
            cell_img = gray[y:y+h, x:x+w]
            
            # OCR识别单元格内容
            cell_text = pytesseract.image_to_string(cell_img, config='--psm 6').strip()
            
            cells_data.append((x, y, w, h, cell_text))
        
        # 根据单元格位置构建表格
        # 这里简化处理，直接返回所有单元格文本
        if cells_data:
            # 将单元格数据转换为DataFrame
            df = pd.DataFrame(cells_data, columns=['x', 'y', 'width', 'height', 'text'])
            
            # 只保留文本列
            text_df = pd.DataFrame({'Cell_Content': df['text']})
            return [text_df]
        
        return None
    except Exception as e:
        print(f"提取网格表格时出错: {str(e)}")
        return None

def ocr_process_page(page):
    """使用OCR处理页面，尝试识别表格"""
    if not OCR_AVAILABLE:
        return None
    
    try:
        # 提取页面图像
        img = extract_image_from_pdf(page)
        
        # 首先检测是否存在网格线表格
        if detect_grid_table(img):
            print("检测到网格线表格，使用专用方法提取...")
            grid_tables = extract_grid_table(img)
            if grid_tables:
                return grid_tables
        
        # 如果没有网格线表格或提取失败，使用常规OCR方法
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 使用OCR直接识别整个页面
        text = pytesseract.image_to_string(gray, lang='eng')
        
        # 分析文本，尝试构建表格
        lines = text.strip().split('\n')
        table_data = []
        
        for line in lines:
            if not line.strip():
                continue
            # 使用空格分割单元格
            cells = re.split(r'\s{2,}', line)
            table_data.append(cells)
        
        if len(table_data) > 1:  # 至少有两行才算表格
            df = pd.DataFrame(table_data)
            return [df]  # 返回DataFrame列表
        
        return None
    except Exception as e:
        print(f"OCR处理过程中出错: {str(e)}")
        return None

def extract_material_list(pdf_path):
    # 声明使用全局变量
    global OCR_AVAILABLE
    
    doc = fitz.open(pdf_path)
    all_tables_by_page = {}  # 按页码存储表格
    
    # 如果OCR可用，设置Tesseract路径
    if OCR_AVAILABLE:
        try:
            pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
        except Exception as e:
            print(f"设置Tesseract路径时出错: {str(e)}")
            OCR_AVAILABLE = False
    
    # 对每一页进行处理
    for page_num, page in enumerate(doc):
        print(f"处理第 {page_num+1} 页...")
        
        # 首先尝试使用OCR方法，因为图片中显示的是网格线表格
        if OCR_AVAILABLE:
            print(f"第 {page_num+1} 页使用OCR处理...")
            ocr_tables = ocr_process_page(page)
            if ocr_tables:
                all_tables_by_page[page_num] = ocr_tables
                print(f"OCR在第 {page_num+1} 页找到表格")
                continue  # 如果OCR成功，跳过其他方法
        
        # 如果OCR失败，尝试PyMuPDF的方法
        # 尝试方法1: 使用PyMuPDF的表格识别 - 标准参数
        table_finder = page.find_tables(
            vertical_strategy="text",
            horizontal_strategy="text",
            snap_tolerance=10,
            snap_x_tolerance=10,
            snap_y_tolerance=10,
            text_tolerance=10,
            text_x_tolerance=10,
            text_y_tolerance=10
        )
        
        tables = table_finder.tables
        
        # 如果没有找到表格，尝试方法2: 使用更宽松的参数
        if not tables:
            table_finder = page.find_tables(
                vertical_strategy="text",
                horizontal_strategy="text",
                snap_tolerance=30,
                snap_x_tolerance=30,
                snap_y_tolerance=30,
                text_tolerance=30,
                text_x_tolerance=30,
                text_y_tolerance=30,
                edge_min_length=3
            )
            tables = table_finder.tables
        
        # 如果仍然没有找到表格，尝试方法3: 使用线条策略
        if not tables:
            table_finder = page.find_tables(
                vertical_strategy="lines",
                horizontal_strategy="lines",
                snap_tolerance=30,
                snap_x_tolerance=30,
                snap_y_tolerance=30,
                text_tolerance=30,
                text_x_tolerance=30,
                text_y_tolerance=30,
                edge_min_length=3
            )
            tables = table_finder.tables
        
        # 如果找到了表格，处理它们
        if tables:
            if page_num not in all_tables_by_page:
                all_tables_by_page[page_num] = []
            
            for table in tables:
                table_data = table.extract()
                df = pd.DataFrame(table_data)
                
                # 保存原始表格数据
                all_tables_by_page[page_num].append(df)
        
        # 如果仍然没有找到表格，尝试方法4: 使用OCR
        elif OCR_AVAILABLE and page_num not in all_tables_by_page:
            print(f"第 {page_num+1} 页使用OCR处理...")
            ocr_tables = ocr_process_page(page)
            if ocr_tables:
                all_tables_by_page[page_num] = ocr_tables
                print(f"OCR在第 {page_num+1} 页找到表格")
    
    doc.close()
    
    # 导出到Excel，每页一个sheet
    if any(all_tables_by_page.values()):
        try:
            # 处理文件名中的特殊字符
            base_name = os.path.basename(pdf_path)
            file_name = os.path.splitext(base_name)[0].replace(' ', '_')
            output_path = os.path.join(os.path.dirname(pdf_path), f"{file_name}_BOM.xlsx")
            
            # 使用ExcelWriter将每个页面的表格保存到对应的sheet
            try:
                with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                    for page_num in sorted(all_tables_by_page.keys()):
                        if all_tables_by_page[page_num]:
                            # 如果一页有多个表格，合并它们
                            if len(all_tables_by_page[page_num]) > 1:
                                combined_df = pd.concat(all_tables_by_page[page_num], ignore_index=True)
                            else:
                                combined_df = all_tables_by_page[page_num][0]
                            
                            sheet_name = f'Page_{page_num+1}'
                            combined_df.to_excel(writer, sheet_name=sheet_name, index=False)
                print(f"已成功导出到: {output_path}")
            except PermissionError:
                # 如果文件被占用，尝试使用新的文件名
                import time
                timestamp = int(time.time())
                new_output_path = f"{output_path.rsplit('.', 1)[0]}_{timestamp}.xlsx"
                with pd.ExcelWriter(new_output_path, engine='openpyxl') as writer:
                    for page_num in sorted(all_tables_by_page.keys()):
                        if all_tables_by_page[page_num]:
                            # 如果一页有多个表格，合并它们
                            if len(all_tables_by_page[page_num]) > 1:
                                combined_df = pd.concat(all_tables_by_page[page_num], ignore_index=True)
                            else:
                                combined_df = all_tables_by_page[page_num][0]
                            
                            sheet_name = f'Page_{page_num+1}'
                            combined_df.to_excel(writer, sheet_name=sheet_name, index=False)
                print(f"原文件被占用，已导出到新文件: {new_output_path}")
        except Exception as e:
            print(f"导出过程中发生错误: {str(e)}")
    else:
        print("未找到任何表格")

def auto_right_click():
    try:
        while True:
            pyautogui.click(button='right')
            # 每秒3次，间隔大约0.33秒，加入一定的随机性
            interval = random.uniform(0.28, 0.38)
            time.sleep(interval)
    except KeyboardInterrupt:
        print("已停止自动点击。")

if __name__ == "__main__":
    print("3秒后开始自动右键点击，按Ctrl+C停止。")
    time.sleep(3)
    auto_right_click()