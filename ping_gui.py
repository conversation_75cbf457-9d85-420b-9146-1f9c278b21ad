import tkinter as tk
from tkinter.scrolledtext import ScrolledText
import subprocess
import threading

class PingApp:
    def __init__(self, master):
        self.master = master
        master.title("Ping 工具 - 实时输出")
        master.geometry("1500x1100")
        master.configure(bg="#f7f7f7")
        
        self.label = tk.Label(master, text="目标地址:", font=("微软雅黑", 12), bg="#f7f7f7")
        self.label.pack_forget()
        self.entry = tk.Entry(master, width=40, font=("微软雅黑", 12))
        self.entry.pack_forget()
        self.preset_frame = tk.Frame(master, bg="#f7f7f7")
        self.preset_frame.pack_forget()
        self.button_frame = tk.Frame(master, bg="#f7f7f7", highlightbackground="#e0e0e0", highlightthickness=1, bd=0)
        self.button_frame.pack(pady=16)
        self.start_button = tk.Button(self.button_frame, text="开始Ping全部", command=self.start_all_ping, font=("微软雅黑", 12, "bold"), width=15, height=1, bg="#4CAF50", fg="white", activebackground="#388E3C", activeforeground="white", relief=tk.FLAT, bd=0, highlightthickness=0)
        self.start_button.pack(side=tk.LEFT, padx=16)
        self.stop_all_button = tk.Button(self.button_frame, text="全部停止", command=self.stop_all_ping, font=("微软雅黑", 12, "bold"), width=15, height=1, bg="#F44336", fg="white", activebackground="#B71C1C", activeforeground="white", relief=tk.FLAT, bd=0, highlightthickness=0)
        self.stop_all_button.pack(side=tk.LEFT, padx=16)
        
        self.output_frame = tk.Frame(master, bg="#f7f7f7")
        self.output_frame.pack(pady=8, fill=tk.BOTH, expand=True)
        self.output_frame.grid_columnconfigure(0, weight=1)
        
        self.outputs = {}
        self.stop_buttons = {}
        self.ping_threads = {}
        self.ping_processes = {}
        self.running_flags = {}
        self.ip_entries = {}
        
        targets = ["163.com", "***********", "hao123.com"]
        for idx, target in enumerate(targets):
            frame = tk.LabelFrame(self.output_frame, text="", font=("微软雅黑", 12, "bold"), bg="#ffffff", fg="#333", labelanchor="n", bd=0, relief=tk.FLAT, highlightbackground="#bdbdbd", highlightthickness=2)
            frame.grid(row=idx, column=0, padx=24, pady=(0, 18), sticky="ew")
            frame.grid_columnconfigure(0, weight=1)
            # 新增横向Frame用于IP输入框、开始和停止按钮
            top_row = tk.Frame(frame, bg="#ffffff")
            top_row.pack(pady=(10, 0), fill=tk.X)
            # 新增一个内部Frame用于居中排列控件
            center_row = tk.Frame(top_row, bg="#ffffff")
            center_row.pack(anchor="center")
            ip_entry = tk.Entry(center_row, width=22, font=("微软雅黑", 11))
            ip_entry.insert(0, target)
            ip_entry.pack(side=tk.LEFT, padx=(0, 8), ipadx=8, ipady=2)
            start_btn = tk.Button(center_row, text="开始", command=lambda t=target: self.start_ping_for(t), font=("微软雅黑", 11, "bold"), width=8, height=1, bg="#4CAF50", fg="white", activebackground="#388E3C", activeforeground="white", relief=tk.FLAT, bd=0, highlightthickness=0)
            start_btn.pack(side=tk.LEFT, padx=(0, 8), ipadx=6, ipady=2)
            stop_btn = tk.Button(center_row, text="停止", command=lambda t=target: self.stop_ping_for(t), state=tk.DISABLED, font=("微软雅黑", 11, "bold"), width=8, height=1, bg="#2196F3", fg="white", activebackground="#1565C0", activeforeground="white", relief=tk.FLAT, bd=0, highlightthickness=0)
            stop_btn.pack(side=tk.LEFT, ipadx=6, ipady=2)
            self.ip_entries[target] = ip_entry
            output = ScrolledText(frame, height=8, width=90, state=tk.DISABLED, font=("Consolas", 11), wrap=tk.WORD, padx=8, pady=8, bg="#f9f9fb", fg="#222", bd=0, relief=tk.FLAT, highlightbackground="#e0e0e0", highlightthickness=1)
            output.pack(pady=(10, 6), padx=10, fill=tk.BOTH, expand=True)
            output.tag_configure("center", justify="center")
            self.outputs[target] = output
            self.stop_buttons[target] = stop_btn
            self.ping_threads[target] = None
            self.ping_processes[target] = None
            self.running_flags[target] = False
        # self.output_frame.columnconfigure(0, weight=1)
        # self.output_frame.columnconfigure(1, weight=1)
        # self.output_frame.columnconfigure(2, weight=1)

    def start_ping_for(self, target):
        # 获取自定义IP
        ip = self.ip_entries[target].get().strip() if self.ip_entries[target].get().strip() else target
        if self.running_flags[target]:
            self.append_output(target, f"{ip} 已在Ping中...\n")
            return
        self.outputs[target].config(state=tk.NORMAL)
        self.outputs[target].delete(1.0, tk.END)
        self.outputs[target].config(state=tk.DISABLED)
        self.running_flags[target] = True
        self.stop_buttons[target].config(state=tk.NORMAL)
        thread = threading.Thread(target=self.ping, args=(target, ip))
        thread.daemon = True
        self.ping_threads[target] = thread
        thread.start()

    def start_all_ping(self):
        targets = ["163.com", "***********", "hao123.com"]
        for target in targets:
            self.start_ping_for(target)

    def stop_ping_for(self, target):
        self.running_flags[target] = False
        if self.ping_processes[target]:
            self.ping_processes[target].terminate()
        self.stop_buttons[target].config(state=tk.DISABLED)

    def start_ping_from_entry(self):
        pass

    def stop_all_ping(self):
        for target in self.outputs:
            self.stop_ping_for(target)

    def ping(self, target, ip=None):
        if ip is None:
            ip = target
        try:
            self.ping_processes[target] = subprocess.Popen([
                "ping", ip, "-t"
            ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, universal_newlines=True)
            for line in self.ping_processes[target].stdout:
                if not self.running_flags[target]:
                    break
                self.append_output(target, line)
            self.ping_processes[target].stdout.close()
        except Exception as e:
            self.append_output(target, f"发生错误: {e}\n")
        finally:
            self.stop_buttons[target].config(state=tk.DISABLED)
            self.running_flags[target] = False

    def append_output(self, target, text):
        def inner():
            self.outputs[target].config(state=tk.NORMAL)
            self.outputs[target].insert(tk.END, text, "center")
            self.outputs[target].see(tk.END)
            self.outputs[target].config(state=tk.DISABLED)
        self.master.after(0, inner)

if __name__ == "__main__":
    root = tk.Tk()
    app = PingApp(root)
    root.mainloop()