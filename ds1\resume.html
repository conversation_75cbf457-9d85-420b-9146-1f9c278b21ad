<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>常曹 - 简历</title>
<style>
* { margin: 0; padding: 0; box-sizing: border-box; }
body { 
  font-family: 'Microsoft YaHei', sans-serif;
  display: flex;
  min-height: 100vh;
}

.sidebar {
  background: #2c3e50;
  color: white;
  width: 280px;
  padding: 40px 30px;
}

.profile {
  text-align: center;
  margin-bottom: 40px;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: #ddd;
  margin: 0 auto 20px;
}

h1 {
  font-size: 24px;
  margin-bottom: 8px;
}

.position {
  color: #bdc3c7;
  font-size: 16px;
}

.contact {
  margin: 30px 0;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-size: 14px;
}

.icon {
  width: 20px;
  margin-right: 12px;
}

.skills {
  margin: 30px 0;
}

.skill-tag {
  display: inline-block;
  background: #34495e;
  padding: 6px 15px;
  border-radius: 15px;
  margin: 5px 3px;
  font-size: 13px;
}

.main-content {
  flex: 1;
  padding: 50px 40px;
  background: white;
}

.section {
  margin-bottom: 40px;
}

h2 {
  color: #2c3e50;
  font-size: 20px;
  border-left: 4px solid #3498db;
  padding-left: 12px;
  margin-bottom: 25px;
}

.timeline-item {
  margin-bottom: 25px;
}

.time {
  color: #7f8c8d;
  font-size: 14px;
  margin-bottom: 6px;
}

h3 {
  font-size: 16px;
  color: #2c3e50;
  margin-bottom: 8px;
}

p {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}
</style>
</head>

<body>
  <div class="sidebar">
    <div class="profile">
      <div class="avatar"></div>
      <h1>常曹</h1>
      <div class="position">高级产品经理</div>
    </div>

    <div class="contact">
      <div class="contact-item">
        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAAdgAAAHYBTnsmCAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAEkSURBVDiNpZIxasQwDEVfBiwPkFPkAjlBbpAj5Ag5Qo6QI+QIOUKOkCPkCDlCjqBdWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWf4HPwZ6wAP4Bp7A3e8D8Aq8gQ/w8jv/AO/AB/gEH7/zD/ABfIJv4ON3/gE+gU/wA3z8zj/AJ/gBvoBP8PE7/wC/wDfwBXyBj9/5B/gFvoEv4Bt8/M4/wB/wA3wDP8DH7/wD/ANm4Bf4B2bW5nO+W8vOAAAAAElFTkSuQmCC" class="icon">
        <span>176****1234</span>
      </div>
      <div class="contact-item">
        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAAdgAAAHYBTnsmCAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAEkSURBVDiNpZIxasQwDEVfBiwPkFPkAjlBbpAj5Ag5Qo6QI+QIOUKOkCPkCDlCjqBdWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWf4HPwZ6wAP4Bp7A3e8D8Aq8gQ/w8jv/AO/AB/gEH7/zD/ABfIJv4ON3/gE+gU/wA3z8zj/AJ/gBvoBP8PE7/wC/wDfwBXyBj9/5B/gFvoEv4Bt8/M4/wB/wA3wDP8DH7/wD/ANm4Bf4B2bW5nO+W8vOAAAAAElFTkSuQmCC" class="icon">
        <span><EMAIL></span>
      </div>
      <div class="contact-item">
        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAAdgAAAHYBTnsmCAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAEkSURBVDiNpZIxasQwDEVfBiwPkFPkAjlBbpAj5Ag5Qo6QI+QIOUKOkCPkCDlCjqBdWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWZZlWf4HPwZ6wAP4Bp7A3e8D8Aq8gQ/w8jv/AO/AB/gEH7/zD/ABfIJv4ON3/gE+gU/wA3z8zj/AJ/gBvoBP8PE7/wC/wDfwBXyBj9/5B/gFvoEv4Bt8/M4/wB/wA3wDP8DH7/wD/ANm4Bf4B2bW5nO+W8vOAAAAAElFTkSuQmCC" class="icon">
        <span>上海·浦东新区</span>
      </div>
    </div>

    <div class="skills">
      <h2>专业技能</h2>
      <div class="skill-tag">Axure</div>
      <div class="skill-tag">Visio</div>
      <div class="skill-tag">Xmind</div>
      <div class="skill-tag">Python</div>
      <div class="skill-tag">SQL</div>
    </div>
  </div>

  <div class="main-content">
    <div class="section">
      <h2>教育背景</h2>
      <div class="timeline-item">
        <div class="time">2013.09 - 2017.06</div>
        <h3>上海交通大学</h3>
        <p>计算机科学与技术专业 本科</p>
      </div>
    </div>

    <div class="section">
      <h2>工作经历</h2>
      <div class="timeline-item">
        <div class="time">2021.03 - 至今</div>
        <h3>某互联网大厂 · 高级产品经理</h3>
        <p>负责B端产品规划，完成3个千万级项目交付</p>
      </div>
    </div>

    <div class="section">
      <h2>项目经验</h2>
      <div class="timeline-item">
        <div class="time">2022.05 - 2023.02</div>
        <h3>智能客服系统升级</h3>
        <p>主导需求分析，完成PRD文档编写，协调10人团队完成项目落地</p>
      </div>
    </div>
  </div>
</body>
</html>