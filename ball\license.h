#ifndef LICENSE_H
#define LICENSE_H

#include <windows.h>
#include <stdio.h>
#include <time.h>

#define LICENSE_FILE "license.dat"
#define DEFAULT_USES 2
#define CONTACT_NUMBER L"13771136253"
#define HARDWARE_ID_LENGTH 32

// 激活码结构
typedef struct {
    int remainingUses;  // 剩余使用次数
    DWORD checksum;     // 校验和
    time_t activateTime; // 激活时间
    char hardwareId[HARDWARE_ID_LENGTH]; // 硬件ID
} License;

// 函数声明
BOOL InitializeLicense(void);
BOOL CheckLicense(void);
BOOL DecrementUses(void);
BOOL ValidateActivationCode(const wchar_t* code);
void ShowActivationDialog(HWND hwnd);
int GetRemainingUses(void);
void GetHardwareId(char* hardwareId);

#endif 