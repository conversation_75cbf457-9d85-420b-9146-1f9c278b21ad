import cv2
import numpy as np
import torch
import os
import logging
from datetime import datetime
from torchvision.models import detection
from config import Config
from database import Database

class GarbageDetector:
    def __init__(self):
        self._setup_logging()
        self._setup_directories()
        self._setup_model()
        self.db = Database()
        self.frame_count = 0
        self.detection_count = {}
        
    def _setup_logging(self):
        os.makedirs(os.path.dirname(Config.LOG_FILE), exist_ok=True)
        logging.basicConfig(
            filename=Config.LOG_FILE,
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        
    def _setup_directories(self):
        os.makedirs(Config.DETECTION_SAVE_PATH, exist_ok=True)
        
    def _setup_model(self):
        try:
            # 加载预训练模型
            self.model = detection.fasterrcnn_resnet50_fpn(pretrained=True)
            self.model.eval()
            self.device = torch.device(Config.DEVICE if torch.cuda.is_available() else "cpu")
            self.model.to(self.device)
            logging.info(f"模型加载成功，使用设备: {self.device}")
        except Exception as e:
            logging.error(f"模型加载失败: {str(e)}")
            raise

    def process_frame(self, frame):
        try:
            # 调整图像大小
            frame = cv2.resize(frame, (Config.FRAME_WIDTH, Config.FRAME_HEIGHT))
            
            # 预处理图像
            image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            image = torch.from_numpy(image.transpose((2, 0, 1))).float()
            image = image.unsqueeze(0)
            image = image.to(self.device)

            # 进行检测
            with torch.no_grad():
                predictions = self.model(image)

            return frame, predictions
        except Exception as e:
            logging.error(f"处理帧时出错: {str(e)}")
            return frame, None

    def save_detection_image(self, frame):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"detection_{timestamp}.jpg"
        filepath = os.path.join(Config.DETECTION_SAVE_PATH, filename)
        cv2.imwrite(filepath, frame)
        return filepath

    def draw_detections(self, frame, predictions):
        if predictions is None:
            return frame

        boxes = predictions[0]['boxes'].cpu().numpy()
        labels = predictions[0]['labels'].cpu().numpy()
        scores = predictions[0]['scores'].cpu().numpy()

        detections = []
        for box, label, score in zip(boxes, labels, scores):
            if score > Config.CONFIDENCE_THRESHOLD:
                x1, y1, x2, y2 = box.astype(int)
                garbage_type = Config.GARBAGE_CLASSES[label - 1]
                category = Config.GARBAGE_TYPES.get(garbage_type, '未分类')
                
                # 绘制边界框
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                # 添加标签
                label_text = f"{garbage_type}: {score:.2f}"
                cv2.putText(frame, label_text, (x1, y1-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
                
                # 添加分类信息
                category_text = f"类别: {category}"
                cv2.putText(frame, category_text, (x1, y1-30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
                
                detections.append((garbage_type, score))
                
                # 更新检测计数
                self.detection_count[garbage_type] = self.detection_count.get(garbage_type, 0) + 1

        return frame, detections

    def detect_garbage(self, rtsp_url=None):
        if rtsp_url is None:
            rtsp_url = Config.RTSP_URL

        cap = cv2.VideoCapture(rtsp_url)
        if not cap.isOpened():
            logging.error(f"无法打开视频流: {rtsp_url}")
            return

        logging.info(f"开始检测垃圾 - 视频流: {rtsp_url}")
        
        try:
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break

                self.frame_count += 1
                
                # 处理帧
                frame, predictions = self.process_frame(frame)
                
                # 绘制检测结果
                frame, detections = self.draw_detections(frame, predictions)
                
                # 保存检测结果到数据库
                if detections and Config.SAVE_DETECTION:
                    image_path = self.save_detection_image(frame)
                    for garbage_type, confidence in detections:
                        self.db.add_detection(garbage_type, confidence, image_path)
                        self.db.update_statistics(garbage_type)
                
                # 显示统计信息
                self._draw_statistics(frame)
                
                # 显示结果
                cv2.imshow('Garbage Detection', frame)
                
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break

        except Exception as e:
            logging.error(f"检测过程中出错: {str(e)}")
        finally:
            cap.release()
            cv2.destroyAllWindows()
            self.db.close()
            self._log_session_summary()

    def _draw_statistics(self, frame):
        # 在画面右上角显示统计信息
        y_offset = 30
        for garbage_type, count in self.detection_count.items():
            text = f"{garbage_type}: {count}"
            cv2.putText(frame, text, (frame.shape[1] - 300, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
            y_offset += 20

    def _log_session_summary(self):
        logging.info("检测会话结束")
        logging.info(f"总处理帧数: {self.frame_count}")
        for garbage_type, count in self.detection_count.items():
            logging.info(f"{garbage_type} 检测次数: {count}")

if __name__ == "__main__":
    detector = GarbageDetector()
    detector.detect_garbage() 