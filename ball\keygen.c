#include <windows.h>
#include <stdio.h>
#include <time.h>

#define WINDOW_WIDTH 400
#define WINDOW_HEIGHT 200
#define IDC_ENCRYPT_EDIT 101
#define IDC_USES_EDIT 102
#define IDC_GENERATE_BTN 103
#define IDC_RESULT_EDIT 104

LRESULT CALLBACK WndProc(HWND, UINT, WPARAM, LPARAM);
void GenerateActivationCode(const char* encryptCode, int uses, char* activationCode);

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, 
    LPSTR lpCmdLine, int nCmdShow)
{
    WNDCLASSEX wc = {0};
    wc.cbSize = sizeof(WNDCLASSEX);
    wc.lpfnWndProc = WndProc;
    wc.hInstance = hInstance;
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW+1);
    wc.lpszClassName = "KeygenClass";
    RegisterClassEx(&wc);

    HWND hwnd = CreateWindowEx(
        0, "KeygenClass", "Activation Code Generator",
        WS_OVERLAPPEDWINDOW & ~(WS_MAXIMIZEBOX | WS_THICKFRAME),
        CW_USEDEFAULT, CW_USEDEFAULT, WINDOW_WIDTH, WINDOW_HEIGHT,
        NULL, NULL, hInstance, NULL);

    ShowWindow(hwnd, nCmdShow);
    UpdateWindow(hwnd);

    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return msg.wParam;
}

LRESULT CALLBACK WndProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam)
{
    static HWND hEncryptEdit, hUsesEdit, hGenerateBtn, hResultEdit;

    switch(msg)
    {
        case WM_CREATE:
        {
            // Create controls
            CreateWindowEx(0, "STATIC", "Encrypt Code:",
                WS_CHILD | WS_VISIBLE,
                20, 20, 70, 20,
                hwnd, NULL, NULL, NULL);

            hEncryptEdit = CreateWindowEx(0, "EDIT", "",
                WS_CHILD | WS_VISIBLE | WS_BORDER | ES_UPPERCASE,
                100, 20, 200, 20,
                hwnd, (HMENU)IDC_ENCRYPT_EDIT, NULL, NULL);

            CreateWindowEx(0, "STATIC", "Uses:",
                WS_CHILD | WS_VISIBLE,
                20, 50, 70, 20,
                hwnd, NULL, NULL, NULL);

            hUsesEdit = CreateWindowEx(0, "EDIT", "10",
                WS_CHILD | WS_VISIBLE | WS_BORDER | ES_NUMBER,
                100, 50, 60, 20,
                hwnd, (HMENU)IDC_USES_EDIT, NULL, NULL);

            hGenerateBtn = CreateWindowEx(0, "BUTTON", "Generate",
                WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
                100, 80, 100, 30,
                hwnd, (HMENU)IDC_GENERATE_BTN, NULL, NULL);

            hResultEdit = CreateWindowEx(0, "EDIT", "",
                WS_CHILD | WS_VISIBLE | WS_BORDER | ES_READONLY,
                20, 120, 350, 20,
                hwnd, (HMENU)IDC_RESULT_EDIT, NULL, NULL);

            // Set default font
            HFONT hFont = CreateFont(16, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE,
                DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
                DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, "Arial");

            SendMessage(hEncryptEdit, WM_SETFONT, (WPARAM)hFont, TRUE);
            SendMessage(hUsesEdit, WM_SETFONT, (WPARAM)hFont, TRUE);
            SendMessage(hGenerateBtn, WM_SETFONT, (WPARAM)hFont, TRUE);
            SendMessage(hResultEdit, WM_SETFONT, (WPARAM)hFont, TRUE);
            break;
        }

        case WM_COMMAND:
        {
            if (LOWORD(wParam) == IDC_GENERATE_BTN) {
                char encryptCode[32] = {0};
                char usesStr[32] = {0};
                char activationCode[50] = {0};
                int uses;

                GetWindowTextA(hEncryptEdit, encryptCode, sizeof(encryptCode));
                GetWindowTextA(hUsesEdit, usesStr, sizeof(usesStr));
                uses = atoi(usesStr);

                if (strlen(encryptCode) != 8) {
                    MessageBoxA(hwnd, "Encrypt code must be 8 characters!", "Error", MB_ICONERROR);
                    return 0;
                }

                if (uses <= 0 || uses > 9999) {
                    MessageBoxA(hwnd, "Uses must be between 1-9999!", "Error", MB_ICONERROR);
                    return 0;
                }

                GenerateActivationCode(encryptCode, uses, activationCode);
                SetWindowTextA(hResultEdit, activationCode);
            }
            break;
        }

        case WM_DESTROY:
            PostQuitMessage(0);
            break;

        default:
            return DefWindowProc(hwnd, msg, wParam, lParam);
    }
    return 0;
}

void GenerateActivationCode(const char* encryptCode, int uses, char* activationCode)
{
    // encryptCode is actually machine ID
    GenerateActivationCode(encryptCode, uses, activationCode);
} 