#!/bin/bash

echo "🚀 排序算法测试程序"
echo "=================="

echo ""
echo "📝 编译 Java 文件..."
javac -encoding UTF-8 FastSortingAlgorithms.java
if [ $? -ne 0 ]; then
    echo "❌ FastSortingAlgorithms.java 编译失败"
    exit 1
fi

javac -encoding UTF-8 SortingAlgorithmTest.java
if [ $? -ne 0 ]; then
    echo "❌ SortingAlgorithmTest.java 编译失败"
    exit 1
fi

echo "✅ 编译成功！"

echo ""
echo "🎯 运行排序算法演示..."
echo "======================"
java FastSortingAlgorithms

echo ""
echo ""
echo "🧪 运行完整测试套件..."
echo "===================="
java SortingAlgorithmTest

echo ""
echo "✅ 所有程序运行完成！"
