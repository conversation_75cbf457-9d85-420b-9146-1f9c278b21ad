version: '3.8'
services:
  ruoyi-mysql:
    image: mysql:5.7
    container_name: ruoyi-mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_ROOT_HOST: '%'
    command:
      - --character-set-server=utf8mb4
      - --collation-server=utf8mb4_unicode_ci
      - --bind-address=0.0.0.0
    ports:
      - "3305:3306"
    volumes:
      - ruoyi-mysql-data:/var/lib/mysql
    # restart: always  # 暂时移除
    healthcheck:
      test: >
        /bin/sh -c "
          mysqladmin ping -h localhost -u root -p$$MYSQL_ROOT_PASSWORD &&
          mysql -u root -p$$MYSQL_ROOT_PASSWORD -e 'USE cdaid;'
        "
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ruoyi-net
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  ruoyi-redis:
    image: redis:latest
    container_name: ruoyi-redis
    ports:
      - "6378:6379"
    volumes:
      - ruoyi-redis-data:/data
    # restart: always # 暂时移除
    networks:
      - ruoyi-net
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  ruoyi-backend:
    image: rouyibackend:latest
    container_name: ruoyi-backend
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_DATASOURCE_DRUID_MASTER_URL=************************************************************************************************************************************************
      - SPRING_DATASOURCE_DRUID_MASTER_USERNAME=root
      - SPRING_DATASOURCE_DRUID_MASTER_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - SPRING_REDIS_HOST=ruoyi-redis
      - SPRING_REDIS_PORT=6379
    ports:
      - "8077:8899"
    depends_on:
      ruoyi-mysql:
        condition: service_healthy
      ruoyi-redis:
        condition: service_started
    networks:
      - ruoyi-net
    restart: on-failure
    working_dir: /app
    volumes:
      - ./logs:/app/logs
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    command: >
      /bin/sh -c "
        echo 'Waiting for network to be ready...' &&
        until ping -c 1 ruoyi-mysql > /dev/null 2>&1; do
          echo 'Waiting for ruoyi-mysql to be reachable...' &&
          sleep 2
        done &&
        echo 'Network is ready, starting application...' &&
        ls -la &&
        pwd &&
        java $JAVA_OPTS -jar cdaid.jar
      "
  ruoyi-frontend:
    image: rouyifrontend:latest
    container_name: ruoyi-frontend
    ports:
      - "6677:80"
    depends_on:
      - ruoyi-backend
    # restart: always # 暂时移除
    networks:
      - ruoyi-net
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  ruoyi-mysql-data:
  ruoyi-redis-data:

networks:
  ruoyi-net:
    driver: bridge