server {
    listen       80;
    server_name  localhost;

    location /front/ {
        alias   /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
    }
f
    location /prod-api/ {
        proxy_pass http://ruoyi-backend:8899/; # 修改代理端口
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
