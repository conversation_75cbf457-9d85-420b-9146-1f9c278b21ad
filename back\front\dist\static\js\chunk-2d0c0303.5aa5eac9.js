(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c0303"],{"415e":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"物资类型",prop:"parentId"}},[a("el-select",{attrs:{placeholder:"请选择物资类型",filterable:"",clearable:""},on:{change:e.handleQuery},model:{value:e.queryParams.parentId,callback:function(t){e.$set(e.queryParams,"parentId",t)},expression:"queryParams.parentId"}},e._l(e.parentList,(function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.id}})})),1)],1),a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.common_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:info:add"],expression:"['asset:info:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:info:add"],expression:"['asset:info:add']"}],attrs:{type:"danger",plain:"",icon:"el-icon-help",size:"mini"},on:{click:e.handleType}},[e._v("物资类型 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.infoList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"名称",align:"center",prop:"name"}}),a("el-table-column",{attrs:{label:"物资类型",align:"center",prop:"parentName"}}),a("el-table-column",{attrs:{label:"告警数量",align:"center",prop:"alarmNum"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.common_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"图片",align:"center",prop:"picData",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("image-preview",{attrs:{src:e.row.picData,width:50,height:50}})]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:info:edit"],expression:"['asset:info:edit']"}],staticStyle:{color:"#55ed27"},attrs:{size:"mini",type:"text",icon:"el-icon-help"},on:{click:function(a){return e.handleItem(t.row)}}},[e._v("明细 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:info:edit"],expression:"['asset:info:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:info:remove"],expression:"['asset:info:remove']"}],staticStyle:{color:"#ff0000"},attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"900px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"物资类型",prop:"parentId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择物资类型",filterable:""},model:{value:e.form.parentId,callback:function(t){e.$set(e.form,"parentId",t)},expression:"form.parentId"}},e._l(e.parentList,(function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.id}})})),1)],1),a("el-row",{attrs:{gutter:15}},[a("el-col",{attrs:{span:14}},[a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.common_status,(function(t){return a("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label)+" ")])})),1)],1)],1)],1),a("el-row",{attrs:{gutter:15}},[a("el-col",{attrs:{span:14}},[a("el-form-item",{attrs:{label:"告警数量",prop:"alarmNum"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-radio-group",{attrs:{size:"large"},on:{change:e.changeAlarmFlag},model:{value:e.alarmFlag,callback:function(t){e.alarmFlag=t},expression:"alarmFlag"}},[a("el-radio-button",{attrs:{label:"告警"}}),a("el-radio-button",{attrs:{label:"不告警"}})],1)],1),"告警"===e.alarmFlag?a("el-col",{attrs:{span:12}},[a("el-input-number",{attrs:{min:0,max:9999,precision:0,placeholder:"请输入告警数量"},model:{value:e.form.alarmNum,callback:function(t){e.$set(e.form,"alarmNum",t)},expression:"form.alarmNum"}})],1):e._e()],1)],1)],1)],1),a("el-form-item",{attrs:{label:"图片",prop:"picData"}},[a("image-upload",{attrs:{limit:1},model:{value:e.form.picData,callback:function(t){e.$set(e.form,"picData",t)},expression:"form.picData"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入内容"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("asset-type",{ref:"assetTypeRef"}),a("asset-template",{ref:"assetTemplateRef"}),a("asset-item",{ref:"assetItemRef"})],1)},l=[],n=a("5530"),s=(a("d81d"),a("ac81")),i=a("fe30"),o=a("0e94"),u=a("40d51"),c=(a("fcb7"),{name:"Info",components:{AssetTemplate:o["default"],AssetItem:u["default"],AssetType:i["default"]},dicts:["common_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,infoList:[],parentList:[],title:"",open:!1,selectedInfo:{},alarmFlag:"不告警",queryParams:{pageNum:1,pageSize:10,parentId:null,ancestors:null,name:null,type:"1",orderNum:null,alarmNum:null,status:null},form:{},rules:{parentId:[{required:!0,message:"物资类型不能为空",trigger:"change"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}],alarmNum:[{required:!0,message:"告警数量不能为空",trigger:"blur"}],orderNum:[{required:!0,message:"顺序不能为空",trigger:"blur"}],name:[{required:!0,message:"名称不能为空",trigger:"blur"}],type:[{required:!0,message:"类型",trigger:"change"}],deptId:[{required:!0,message:"区库不能为空",trigger:"change"}]}}},created:function(){this.getList(),this.getParentList()},methods:{getList:function(){var e=this;this.loading=!0,Object(s["d"])(this.queryParams).then((function(t){e.infoList=t.rows,e.total=t.total,e.loading=!1})),this.getDeptOption()},getParentList:function(){var e=this;Object(s["f"])({type:"0",status:"0"}).then((function(t){e.parentList=t.data}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,parentId:null,ancestors:null,name:null,type:"1",orderNum:1,alarmNum:null,status:"0",picData:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.getParentList(),this.open=!0,this.title="添加物资信息",this.getDeptOption()},handleUpdate:function(e){var t=this;this.reset(),this.getParentList();var a=e.id||this.ids;Object(s["c"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改物资信息"})),this.getDeptOption()},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?Object(s["g"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(s["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除物资信息编号为"'+a+'"的数据项？').then((function(){return Object(s["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("asset/info/export",Object(n["a"])({},this.queryParams),"info_".concat((new Date).getTime(),".xlsx"))},handleType:function(){this.$refs["assetTypeRef"].openDialog()},handleItem:function(e){this.$refs["assetItemRef"].openDialog(e)},handleTemplate:function(){this.$refs["assetTemplateRef"].openDialog()},changeAlarmFlag:function(e){this.form.alarmNum="告警"===e?0:-1}}}),m=c,p=a("2877"),d=Object(p["a"])(m,r,l,!1,null,null,null);t["default"]=d.exports}}]);