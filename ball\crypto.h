#ifndef CRYPTO_H
#define CRYPTO_H

#include <windows.h>
#include <time.h>

#define KEY_LENGTH 16
#define MACHINE_ID_LENGTH 32
#define ACTIVATION_CODE_LENGTH 24

typedef struct {
    char machineId[MACHINE_ID_LENGTH];
    DWORD expiryDate;
    int maxUses;
    DWORD checksum;
} LICENSE_DATA;

// Get unique machine ID
void GetMachineID(char* machineId);

// Generate activation code from machine ID and uses
void GenerateActivationCode(const char* machineId, int uses, char* activationCode);

// Validate activation code and extract license data
BOOL ValidateActivationCode(const char* activationCode, LICENSE_DATA* licenseData);

// Save/Load license data
BOOL SaveLicenseData(const LICENSE_DATA* licenseData);
BOOL LoadLicenseData(LICENSE_DATA* licenseData);

#endif 