垃圾检测系统实现指南
====================

1. 项目结构
------------------
project/
├── config.py          # 配置文件
├── database.py        # 数据库操作
├── garbage_detection.py # 主检测程序
├── train.py          # 训练程序
├── transforms.py      # 数据增强
├── requirements.txt   # 依赖包
├── models/           # 模型保存目录
├── logs/             # 日志目录
├── detections/       # 检测结果保存目录
└── dataset/          # 数据集目录
    ├── train/        # 训练集
    │   ├── images/   # 训练图像
    │   └── annotations/ # 训练标注
    └── val/          # 验证集
        ├── images/   # 验证图像
        └── annotations/ # 验证标注

2. 环境配置
------------------
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
.\venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

3. 依赖包 (requirements.txt)
------------------
opencv-python>=4.5.0
numpy>=1.19.0
torch>=1.9.0
torchvision>=0.10.0
mysql-connector-python>=8.0.0
python-dotenv>=0.19.0
pillow>=8.0.0
matplotlib>=3.3.0

4. 配置文件 (config.py)
------------------
class Config:
    # RTSP配置
    RTSP_URL = "rtsp://your_camera_url"
    
    # 模型配置
    MODEL_PATH = "models/garbage_detector.pth"
    DEVICE = "cuda"  # 或 "cpu"
    CONFIDENCE_THRESHOLD = 0.5
    
    # 垃圾分类
    GARBAGE_CLASSES = [
        'plastic_bottle', 'glass_bottle', 'paper', 'cardboard',
        'metal_can', 'plastic_bag', 'food_waste', 'battery',
        'electronic_waste', 'clothing'
    ]
    
    # 垃圾类型对应的处理方法
    GARBAGE_TYPES = {
        'plastic_bottle': '可回收',
        'glass_bottle': '可回收',
        'paper': '可回收',
        'cardboard': '可回收',
        'metal_can': '可回收',
        'plastic_bag': '可回收',
        'food_waste': '厨余垃圾',
        'battery': '有害垃圾',
        'electronic_waste': '有害垃圾',
        'clothing': '可回收'
    }
    
    # 视频处理配置
    FRAME_WIDTH = 1280
    FRAME_HEIGHT = 720
    FPS = 30
    
    # 存储配置
    SAVE_DETECTION = True
    DETECTION_SAVE_PATH = "detections/"
    LOG_FILE = "logs/garbage_detection.log"
    
    # 数据库配置
    DB_HOST = "localhost"
    DB_NAME = "garbage_detection"
    DB_USER = "user"
    DB_PASSWORD = "password"

5. 数据库设置
------------------
1. 安装MySQL数据库
2. 创建数据库和用户：
   ```sql
   CREATE DATABASE garbage_detection;
   CREATE USER 'user'@'localhost' IDENTIFIED BY 'password';
   GRANT ALL PRIVILEGES ON garbage_detection.* TO 'user'@'localhost';
   FLUSH PRIVILEGES;
   ```

6. 数据集准备
------------------
1. 创建数据集目录结构：
   mkdir -p dataset/train/images
   mkdir -p dataset/train/annotations
   mkdir -p dataset/val/images
   mkdir -p dataset/val/annotations

2. 数据集要求：
   - 每类垃圾至少100张图片
   - 包含不同角度和光照条件
   - 验证集占总数据20%
   
3. 标注格式：
   - 每个图像对应一个txt标注文件
   - 每行格式：class_id x_min y_min x_max y_max
   - 例如：1 100 150 300 400

7. 训练过程
------------------
1. 准备数据集
2. 配置config.py中的参数
3. 运行训练：
   python train.py

训练参数：
- 批次大小：2（可调整）
- 学习率：0.005
- 动量：0.9
- 权重衰减：0.0005
- 训练轮数：10

数据增强方式：
- 随机水平翻转
- 随机裁剪
- 随机缩放
- 随机颜色扰动

8. 运行检测
------------------
1. 确保模型已训练完成
2. 配置RTSP流地址
3. 运行检测程序：
   python garbage_detection.py

9. 注意事项
------------------
1. 硬件要求：
   - 建议使用GPU训练
   - 至少8GB显存
   - CPU训练需要更长时间

2. 优化建议：
   - 如果训练效果不好：
     * 增加训练数据
     * 调整学习率
     * 增加训练轮数
     * 修改数据增强策略
     * 尝试不同优化器

3. 维护建议：
   - 定期备份模型文件
   - 监控GPU内存使用
   - 定期检查日志文件
   - 保持数据库备份

10. 故障排除
------------------
1. 如果无法连接摄像头：
   - 检查RTSP地址是否正确
   - 确认网络连接
   - 检查摄像头是否在线

2. 如果检测效果不好：
   - 检查模型训练是否充分
   - 调整置信度阈值
   - 增加训练数据
   - 检查数据质量

3. 如果系统崩溃：
   - 检查日志文件
   - 确认内存使用情况
   - 检查GPU状态
   - 确保数据库连接正常

11. 代码维护
------------------
- 定期更新依赖包
- 备份重要数据
- 监控系统性能
- 记录系统改动

完整代码请参见各个.py文件。如需修改或定制功能，请仔细阅读代码注释。 