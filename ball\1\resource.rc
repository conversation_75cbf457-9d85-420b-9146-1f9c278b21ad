#pragma code_page(65001)
#include <windows.h>
#include "resource.h"

IDD_MAIN_DIALOG DIALOG DISCARDABLE 0, 0, 250, 200
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "坐标插算工具"
FONT 9, "宋体"
BEGIN
    LTEXT           "结?", -1, 20, 20, 20, 8
    LTEXT           "纬?", -1, 100, 20, 20, 8
    LTEXT           "确明新须索", -1, 160, 20, 40, 8
    EDITTEXT        IDC_EDIT1, 20, 35, 60, 14
    EDITTEXT        IDC_EDIT2, 100, 35, 60, 14

    LTEXT           "结?", -1, 20, 60, 20, 8
    LTEXT           "纬?", -1, 100, 60, 20, 8
    LTEXT           "确明新须索", -1, 160, 60, 40, 8
    EDITTEXT        IDC_EDIT3, 20, 75, 60, 14
    EDITTEXT        IDC_EDIT4, 100, 75, 60, 14

    LTEXT           "结?", -1, 20, 100, 20, 8
    LTEXT           "纬?", -1, 100, 100, 20, 8
    LTEXT           "确明新须索", -1, 160, 100, 40, 8
    EDITTEXT        IDC_EDIT5, 20, 115, 60, 14
    EDITTEXT        IDC_EDIT6, 100, 115, 60, 14
    
    PUSHBUTTON      "编 x 辑", IDC_CALC_BUTTON, 20, 150, 50, 14
END