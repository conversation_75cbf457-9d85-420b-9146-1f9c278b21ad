(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e33ba330"],{ac81:function(e,t,a){"use strict";a.d(t,"d",(function(){return r})),a.d(t,"f",(function(){return i})),a.d(t,"e",(function(){return s})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"g",(function(){return u})),a.d(t,"b",(function(){return c}));var n=a("b775");function r(e){return Object(n["a"])({url:"/asset/info/list",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/asset/info/option",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/asset/info/optionByDept",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/asset/info/"+e,method:"get"})}function l(e){return Object(n["a"])({url:"/asset/info",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/asset/info",method:"put",data:e})}function c(e){return Object(n["a"])({url:"/asset/info/"+e,method:"delete"})}},fe30:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"物资类型",visible:e.typeOpen,fullscreen:"",width:"100%","append-to-body":""},on:{"update:visible":function(t){e.typeOpen=t},close:e.closeItem}},[a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"45spx"}},[a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.common_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:info:add"],expression:"['asset:info:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{"min-height":"300px"},attrs:{data:e.infoList,"max-height":500},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"名称",align:"center",prop:"name"}}),a("el-table-column",{attrs:{label:"显示顺序",align:"center",prop:"orderNum"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.common_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:info:edit"],expression:"['asset:info:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:info:remove"],expression:"['asset:info:remove']"}],staticStyle:{color:"#ff0000"},attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"显示顺序",prop:"orderNum"}},[a("el-input-number",{attrs:{min:0,max:99999,precision:0,placeholder:"请输入显示顺序"},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.common_status,(function(t){return a("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label)+" ")])})),1)],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入内容"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)])},r=[],i=a("5530"),s=(a("d81d"),a("ac81")),o={name:"AssetType",dicts:["common_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,infoList:[],title:"",open:!1,typeOpen:!1,queryParams:{pageNum:1,pageSize:10,parentId:null,ancestors:null,name:null,type:"0",orderNum:null,alarmNum:null,status:null},form:{},rules:{parentId:[{required:!0,message:"父级id不能为空",trigger:"change"}],name:[{required:!0,message:"名称不能为空",trigger:"blur"}],type:[{required:!0,message:"类型 0物资类型  1物资信息不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(s["d"])(this.queryParams).then((function(t){e.infoList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,parentId:0,ancestors:0,name:null,type:"0",orderNum:1,alarmNum:null,status:"0",picData:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加物资信息"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;Object(s["c"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改物资信息"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?Object(s["g"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(s["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除物资类型编号为"'+a+'"的数据项？').then((function(){return Object(s["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("asset/info/export",Object(i["a"])({},this.queryParams),"info_".concat((new Date).getTime(),".xlsx"))},openDialog:function(){this.typeOpen=!0,this.resetQuery()},closeItem:function(){this.typeOpen=!1}}},l=o,u=a("2877"),c=Object(u["a"])(l,n,r,!1,null,null,null);t["default"]=c.exports}}]);