## 成都仓储管理平台软件测试报告 

### 测试对象 

#### 本报告针对仓储管理平台的核心功能进行功能测试，包括用户管理、角色管理、急救点位管理、 

#### 区库管理、RFID设备、物资信息、出入库管理、资产一览、告警管理模块，分别对各功能的增删 

#### 改查进行详细测试。 

### 一、用户管理功能测试 

#### 功能描述 ：用户管理模块用于添加、修改、删除和查询系统用户信息，包括用户名、密码、角色 

#### 等字段。 

#### 测试内容 ： 

####  新增用户 ：验证系统是否能成功添加用户，输入用户名、密码、角色等信息后保存，检查用 

#### 户列表是否正确更新。输入特殊字符和空值测试字段校验功能。 

####  删除用户 ：测试删除功能，选择一个用户后删除，验证列表更新，同时确认被删除用户无法 

#### 再登录系统。 

####  修改用户 ：检查编辑功能，修改用户名或角色后保存，验证更新内容是否在用户列表中正确 

#### 显示，确保对登录权限无负面影响。 

####  查询用户 ：测试按用户名或角色查询功能，输入部分关键字检查结果准确性，确保无漏查或 

#### 误查。 

#### 测试结果 ：通过，所有功能均符合预期，操作流畅，提示信息准确。 

### 二、角色管理功能测试 

#### 功能描述 ：角色管理模块用于创建和维护用户角色信息，设定角色权限范围，支持增删改查功 

#### 能。 

#### 测试内容 ： 

####  新增角色 ：验证系统是否能正确创建角色，设置角色名称及权限后保存，检查列表更新情况 

#### 及权限分配是否生效。 

####  删除角色 ：测试删除功能，确保删除角色后无法被关联用户使用，同时未影响其他用户的权 

#### 限。 

####  修改角色 ：测试修改功能，调整角色名称或权限范围后保存，验证更新是否准确无误。 


####  查询角色 ：测试按角色名称搜索，检查查询结果与输入关键字的匹配度，确保精确查询和模 

#### 糊查询均有效。 

#### 测试结果 ：通过，角色新增、删除、修改、查询功能运行正常。 

### 三、急救点位管理功能测试 

#### 功能描述 ：管理急救点位信息，包括点位名称、排序、状态和创建时间。 

#### 测试内容 ： 

####  新增点位 ：输入点位名称及排序信息后保存，验证列表显示是否正确，确保状态字段默认为 

#### 启用状态。 

####  删除点位 ：测试删除功能，删除指定点位后检查是否从系统中移除，同时确认操作记录准 

#### 确。 

####  修改点位 ：修改点位名称或状态后保存，检查点位排序是否按新规则更新。 

####  查询点位 ：按点位名称或状态查询，测试结果展示的正确性和完整性。 

#### 测试结果 ：通过，所有功能操作无异常，点位管理逻辑符合预期。 

### 四、区库管理功能测试 

#### 功能描述 ：管理区库信息，包括区库名称、排序、状态和创建时间。 

#### 测试内容 ： 

####  新增区库 ：添加新区库信息后保存，验证是否正确显示在列表中，排序规则是否正常。 

####  删除区库 ：选择指定区库进行删除，验证其在列表中的移除状态，检查与之关联的功能是否 

#### 正常。 

####  修改区库 ：编辑区库名称、排序等字段后保存，确保列表显示更新。 

####  查询区库 ：按区库名称或状态过滤，验证结果展示的准确性。 

#### 测试结果 ：通过，区库管理模块增删改查功能表现稳定。 

### 五、RFID设备功能测试 

#### 功能描述 ：管理RFID设备信息，包括设备名称、区库状态及运行状态。 

#### 测试内容 ： 


####  新增设备 ：输入设备名称后保存，验证其是否成功添加，初始运行状态是否正确。 

####  删除设备 ：测试删除功能，确保设备删除后不会影响其他功能模块的操作。 

####  修改设备 ：修改设备名称、运行状态或关联区库后保存，验证更新内容在列表中是否正确显 

#### 示。 

####  查询设备 ：通过设备名称或状态查询，检查结果是否准确匹配输入条件。 

#### 测试结果 ：通过，RFID设备模块功能正常，设备信息处理符合业务逻辑。 

### 六、物资信息功能测试 

#### 功能描述 ：管理物资信息，包括物资名称、类型、告警数量、状态、图片和备注。 

#### 测试内容 ： 

####  新增物资 ：输入物资名称、类型等信息后保存，验证其是否正确显示在列表中，图片字段上 

#### 传功能是否正常。 

####  删除物资 ：删除指定物资，确保列表更新正确，同时系统日志记录无异常。 

####  修改物资 ：修改物资类型或告警数量后保存，验证更新内容是否符合预期。 

####  查询物资 ：按物资名称或类型过滤，检查结果是否精准。 

#### 测试结果 ：通过，物资信息模块增删改查功能均表现正常。 

### 七、出入库管理功能测试 

#### 功能描述 ：管理物资的出入库信息，包括区库、物资名称、条码及操作方式。 

#### 测试内容 ： 

####  查询记录 ：按区库或物资名称搜索，验证查询结果的完整性与准确性。 

#### 测试结果 ：通过，出入库管理功能逻辑清晰，运行稳定。 

### 八、资产一览功能测试 

#### 功能描述 ：展示当前区库、物资名称、条码、过期日期、告警状态和状态的综合信息。 

#### 测试内容 ： 

####  查询资产信息 ：通过物资名称或区库过滤，验证结果是否符合预期。 


#### 测试结果 ：通过，资产一览功能正常。 

## 九、告警管理功能测试 

#### 功能描述 ：管理告警信息，包括告警类型、区库、物资条码及告警日期。 

#### 测试内容 ： 

####  查询告警信息 ：通过告警类型或日期范围过滤，检查结果准确性。 

#### 测试结果 ：通过，告警管理功能符合预期，信息处理逻辑清晰。 

# 十、审批管理功能测试 

#### 功能描述 

#### 审批管理模块用于管理审批记录，包括字段：区库、名称、类型、状态、创建人、创建时间、审 

#### 批人、审批时间和原因。 

#### 测试内容 

####  查询审批记录 ：按区库、名称或状态过滤记录，验证查询结果的完整性和准确性，确保符合 

#### 条件的记录均显示在列表中。 

## 总结 

#### 测试验证仓储管理平台所有核心功能均符合需求，系统运行稳定，用户体验良好。下一步建议增 

#### 加性能测试与安全测试，确保平台能在高并发及复杂场景下安全运行。 


