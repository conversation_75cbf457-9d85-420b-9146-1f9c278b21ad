#include "license.h"
#include "resource.h"
#include <wchar.h>
#include <iphlpapi.h>
#include <winreg.h>  // 用于获取系统信息

#pragma comment(lib, "IPHLPAPI.lib")

// 简单的加密密钥
static const DWORD ENCRYPTION_KEY = 0x12345678;

// 计算校验和
static DWORD CalculateChecksum(const License* license) {
    return (license->remainingUses * 31 + (DWORD)license->activateTime) ^ ENCRYPTION_KEY;
}

// 获取MAC地址并生成硬件ID
void GetHardwareId(char* hardwareId) {
    DWORD hash = 0;
    
    // 获取网卡信息
    IP_ADAPTER_INFO AdapterInfo[16];
    DWORD dwBufLen = sizeof(AdapterInfo);
    if (GetAdaptersInfo(AdapterInfo, &dwBufLen) == ERROR_SUCCESS) {
        PIP_ADAPTER_INFO pAdapterInfo = AdapterInfo;
        for (int i = 0; i < 6; i++) {
            hash = hash * 31 + pAdapterInfo->Address[i];
        }
    }
    
    // 获取硬盘序列号
    char volumeName[MAX_PATH];
    char fileSystem[MAX_PATH];
    DWORD serialNumber;
    GetVolumeInformationA("C:\\", volumeName, MAX_PATH, &serialNumber, 
                         NULL, NULL, fileSystem, MAX_PATH);
    hash = hash * 31 + serialNumber;
    
    // 获取系统信息
    SYSTEM_INFO sysInfo;
    GetSystemInfo(&sysInfo);
    hash = hash * 31 + sysInfo.dwProcessorType;
    hash = hash * 31 + sysInfo.dwNumberOfProcessors;
    hash = hash * 31 + sysInfo.dwActiveProcessorMask;
    
    // 获取计算机名
    char computerName[MAX_COMPUTERNAME_LENGTH + 1];
    DWORD size = sizeof(computerName);
    if (GetComputerNameA(computerName, &size)) {
        for (DWORD i = 0; i < size; i++) {
            hash = hash * 31 + computerName[i];
        }
    }
    
    // 获取BIOS信息
    HKEY hKey;
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, 
        "HARDWARE\\DESCRIPTION\\System\\BIOS", 
        0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        char biosInfo[256];
        DWORD dataSize = sizeof(biosInfo);
        if (RegQueryValueExA(hKey, "SystemManufacturer", NULL, NULL, 
            (LPBYTE)biosInfo, &dataSize) == ERROR_SUCCESS) {
            for(DWORD i = 0; i < dataSize; i++) {
                hash = hash * 31 + biosInfo[i];
            }
        }
        RegCloseKey(hKey);
    }
    
    // 生成加密的硬件ID
    for (int i = 0; i < HARDWARE_ID_LENGTH - 1; i++) {
        hash = hash * ********** + 12345;
        hardwareId[i] = ((hash >> 16) & 0x1F) + 'A'; // 只生成A-Z的字符
    }
    hardwareId[HARDWARE_ID_LENGTH - 1] = '\0';
}

// 初始化许可证
BOOL InitializeLicense(void) {
    License license;
    FILE* file = fopen(LICENSE_FILE, "rb");
    
    if (!file) {
        license.remainingUses = DEFAULT_USES;
        license.activateTime = time(NULL);
        GetHardwareId(license.hardwareId);
        license.checksum = CalculateChecksum(&license);
        
        file = fopen(LICENSE_FILE, "wb");
        if (!file) return FALSE;
        
        fwrite(&license, sizeof(License), 1, file);
        fclose(file);
        return TRUE;
    }
    
    fclose(file);
    return TRUE;
}

// 添加新函数：获取剩余使用次数
int GetRemainingUses(void) {
    License license;
    FILE* file = fopen(LICENSE_FILE, "rb");
    
    if (!file) return 0;
    
    if (fread(&license, sizeof(License), 1, file) != 1) {
        fclose(file);
        return 0;
    }
    fclose(file);
    
    // 验证校验和
    if (license.checksum != CalculateChecksum(&license)) return 0;
    
    return license.remainingUses;
}

// 根据硬件ID生成加密码
void GenerateEncryptionCode(const char* hardwareId, char* encryptCode) {
    DWORD hash = 0;
    // 使用硬件ID生成基础hash
    for(int i = 0; hardwareId[i]; i++) {
        hash = hash * 31 + hardwareId[i];
    }
    
    // 添加日期因素
    SYSTEMTIME st;
    GetLocalTime(&st);
    hash = hash * 31 + st.wYear;
    hash = hash * 31 + st.wMonth;
    hash = hash * 31 + st.wDay;
    
    // 生成8位加密码
    for(int i = 0; i < 8; i++) {
        hash = hash * ********** + 12345;
        encryptCode[i] = ((hash >> 16) & 0x1F) + 'A'; // 生成A-Z的字符
    }
    encryptCode[8] = '\0';
}

// 激活对话框回调函数
static INT_PTR CALLBACK ActivateDialogProc(HWND hwnd, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message)
    {
        case WM_INITDIALOG:
        {
            // 获取并显示加密码
            char hardwareId[HARDWARE_ID_LENGTH];
            GetHardwareId(hardwareId);
            char encryptCode[9];
            GenerateEncryptionCode(hardwareId, encryptCode);
            SetDlgItemTextA(hwnd, IDC_ENCRYPT_CODE, encryptCode);
            return TRUE;
        }

        case WM_COMMAND:
        {
            switch (LOWORD(wParam))
            {
                case IDC_ACTIVATE_BTN:
                {
                    wchar_t code[50];
                    GetDlgItemTextW(hwnd, IDC_ACTIVATE_EDIT, code, 50);
                    
                    if (ValidateActivationCode(code)) {
                        MessageBoxW(hwnd, L"激活成功！", L"成功", MB_ICONINFORMATION);
                        EndDialog(hwnd, IDOK);
                    } else {
                        MessageBoxW(hwnd, L"无效的激活码！", L"错误", MB_ICONERROR);
                    }
                    return TRUE;
                }

                case IDCANCEL:
                    EndDialog(hwnd, IDCANCEL);
                    ExitProcess(0);
                    return TRUE;
            }
            break;
        }

        case WM_CLOSE:
            EndDialog(hwnd, IDCANCEL);
            ExitProcess(0);
            return TRUE;
    }
    return FALSE;
}

// 修改ShowActivationDialog函数
void ShowActivationDialog(HWND hwnd)
{
    DialogBox(GetModuleHandle(NULL), MAKEINTRESOURCE(IDD_ACTIVATE_DIALOG), hwnd, ActivateDialogProc);
}

// 修改CheckLicense函数
BOOL CheckLicense(void) {
    License license;
    FILE* file = fopen(LICENSE_FILE, "rb");
    
    if (!file) {
        ShowActivationDialog(NULL);
        return FALSE;
    }
    
    if (fread(&license, sizeof(License), 1, file) != 1) {
        fclose(file);
        ShowActivationDialog(NULL);
        return FALSE;
    }
    fclose(file);
    
    // 验证校验和
    if (license.checksum != CalculateChecksum(&license)) {
        ShowActivationDialog(NULL);
        return FALSE;
    }
    
    if (license.remainingUses <= 0) {
        ShowActivationDialog(NULL);
        return FALSE;
    }
    
    return TRUE;
}

// 修改DecrementUses函数，添加提示
BOOL DecrementUses(void) {
    License license;
    FILE* file = fopen(LICENSE_FILE, "rb+");
    
    if (!file) return FALSE;
    
    if (fread(&license, sizeof(License), 1, file) != 1) {
        fclose(file);
        return FALSE;
    }
    
    if (license.remainingUses <= 0) {
        fclose(file);
        return FALSE;
    }
    
    license.remainingUses--;
    license.checksum = CalculateChecksum(&license);
    
    rewind(file);
    fwrite(&license, sizeof(License), 1, file);
    fclose(file);
    
    // 如果剩余次数较少，显示提示
    if (license.remainingUses <= 1) {
        wchar_t message[100];
        _snwprintf(message, 100, L"警告：仅剩 %d 次使用机会！", license.remainingUses);
        MessageBoxW(NULL, message, L"提示", MB_ICONWARNING);
    }
    
    return TRUE;
}

// 验证激活码
BOOL ValidateActivationCode(const wchar_t* code) {
    // 这里实现激活码验证逻辑
    // 示例：简单的激活码格式 "USES-XXXXXX"
    int newUses;
    wchar_t prefix[5];
    
    if (swscanf(code, L"%4s-%d", prefix, &newUses) != 2) return FALSE;
    if (wcscmp(prefix, L"USES") != 0) return FALSE;
    if (newUses <= 0 || newUses > 9999) return FALSE;
    
    // 验证硬件ID
    char currentHardwareId[HARDWARE_ID_LENGTH];
    GetHardwareId(currentHardwareId);
    
    License license;
    license.remainingUses = newUses;
    license.activateTime = time(NULL);
    strcpy(license.hardwareId, currentHardwareId);
    license.checksum = CalculateChecksum(&license);
    
    FILE* file = fopen(LICENSE_FILE, "wb");
    if (!file) return FALSE;
    
    fwrite(&license, sizeof(License), 1, file);
    fclose(file);
    
    return TRUE;
} 