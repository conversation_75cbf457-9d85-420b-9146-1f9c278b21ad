# MQTT消息投递保证机制说明文档

## 需求
实现MQTT消息：
1. 不丢失
2. 不重复
3. 只被一个客户端接收一次

## 配置方案

### 服务器配置 (/etc/mosquitto/mosquitto.conf)
```conf
# 1. 消息持久化（不丢）
persistence true
persistence_location /var/lib/mosquitto/
persistent_client_expiration 0     # 会话永不过期

# 2. 消息队列（确保存储）
max_queued_messages 100000         # 根据需求调整大小
```

### 客户端连接参数
- `clean_session = false`          # 启用持久会话
- `qos = 2`                        # QoS 2确保不丢不重
- 固定的唯一 `client_id`          # 确保会话持久化

### 订阅主题
- 使用共享订阅: `$share/group1/your_topic`  # 确保消息只被一个客户端接收

## 断线重连机制

### 断线期间
- 消息会存储在 MQTT 服务器上
- 因为 `persistent_client_expiration 0`，消息会永久保存
- 消息按照原有顺序排队等待投递

### 重连时
- 因为设置了 `clean_session = false`
- 客户端使用相同的 `client_id` 重连
- 服务器会识别这是之前的客户端
- 自动开始投递之前未送达的消息

### 消息投递
- 按照原始顺序依次投递
- 因为 `qos = 2`，确保每条消息都会送达
- 共享订阅 `$share` 确保消息只给一个客户端

## 注意事项
1. 因为消息会永久保存，需要监控服务器磁盘空间使用情况
2. 建议定期维护，清理不需要的历史消息
3. 消息队列大小可根据实际需求调整 