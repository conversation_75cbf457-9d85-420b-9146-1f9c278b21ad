# 常先生 - 系统架构师 & 部门主管 & 项目经理

## 个人信息

| 基本信息 |                    | 联系方式 |                   |
| -------- | ------------------ | -------- | ----------------- |
| 出生年月 | 1983年3月          | 电话     | 13771136253       |
| 民族     | 汉族               | 邮箱     | <EMAIL> |
| 学历     | 本科               | 住址     | 江苏无锡新区      |
| 毕业院校 | 常州大学           | 政治面貌 | 群众              |
| 求职意向 | 项目经理、系统架构 | 入职时间 | 一个月内          |

## 教育背景

* **2004.09 - 2007.06** 常州大学 计算机科学与技术(本科)

## 工作经验

* **2020.03 - 至今** 上海杰盛立业网络科技 - 高级技术经理
  * 人工智能、物联网应用、智慧园区等项目
  * 负责团队技术方向规划与项目架构设计

* **2018.01 - 2020.03** 无锡萨弗特智能科技有限公司 - 研发部门主管
  * 部门管理
  * 物联网产品规划，设计，软件架构
  * 项目需求分析，管理开发跟踪
  * 外部项目沟通，资源利用

* **2014.05 - 2018.01** 金蝶集团 - 项目管理技术架构
  * 负责软件需求调研、项目任务书、软件需求规格说明书的编写及确认
  * 技术框架选型，原型设计讨论确认，技术疑问攻关
  * 负责软件项目整体实施的计划编制、核实、确认和发布，跟踪与监控软件项目的实施进度
  * 完成整个项目软件部分以及与硬件接口部分的实施并确保项目的最终验收
  * 负责项目履行过程中与用户，供应商，集成商，公司其它相关部门的协调和沟通

* **2007.09 - 2014.05** 中国太平洋保险公司(外包) - 项目管理技术开发
  * 项目管理
  * 团队管理
  * 软件开发

## 项目经验

### 人工智能与物联网项目

* **2023.07 - 至今** 上海杰盛立业公司内部数据私有知识库人工智能项目
  * **职责**：负责公司内部数据私有知识库的构建和维护，利用自然语言处理技术提供智能问答服务
  * **详细描述**：
    * **知识库构建**：收集和整理公司内部各类数据，构建结构化的知识库
    * **RAG技术应用**：利用Retrieval-Augmented Generation (RAG) 技术，结合知识库中的数据，生成更准确和上下文相关的回答
    * **开发工具**：使用Cursor等开发工具，提高开发效率和代码质量
    * **开源工具**：采用Dify等开源工具，优化系统性能和稳定性
    * **系统集成与优化**：将智能问答系统集成到公司内部办公平台，持续优化系统性能
    * **技术亮点**：通过RAG技术实现了公司内部知识的智能化管理，显著提升员工工作效率

* **2022.01 - 2023.06** 上海科嘉智慧园区项目
  * **职责**：负责智慧园区相关人工智能算法的开发和应用
  * **详细描述**：
    * **人脸识别算法**：采用深度学习框架构建和优化人脸识别模型
    * **车辆识别算法**：开发基于CNN的车辆识别系统，提升园区车辆管理效率
    * **行为识别算法**：利用视频分析技术，开发行为识别算法，提高园区安全防范能力
    * **技术亮点**：成功将多种AI算法集成到园区管理平台，显著提升园区安全性和运营效率

* **2020.07 - 至今** 上海菊园小区垃圾识别项目一期，二期，三期
  * **职责**：负责垃圾识别算法的开发和优化，实现小区垃圾智能化管理
  * **详细描述**：
    * **数据收集与标注**：收集大量垃圾图片数据，进行精细化标注
    * **模型训练与优化**：采用深度学习技术训练垃圾识别模型，提高识别准确率
    * **系统集成与部署**：将垃圾识别算法集成到小区垃圾分类系统中
    * **技术亮点**：通过深度学习技术显著提高了垃圾分类的准确率

* **2018.11 - 2019.06** 企业CMMI3评选项目 - 项目总负责
  * **描述**：CMMI3认证旨在建立企业完整的项目管理体系和标准流程
  * **责任**：
    * 负责整个项目的定义工作安排，培训接洽，人员组织
    * 负责参评项目选型，项目文档规范构建补充
    * 负责参评现场的统筹安排，制定CMMI3的具体流程

### 物联网与新零售项目

* **2018.09 - 2018.11** 新零售无人称重货柜物联网项目(离线版) - 项目管理技术架构
  * **描述**：为满足客户无电要求而定制化改造的项目，结合手机APP二维码扫码技术以及密钥SDK比对技术
  * **责任**：项目管理，需求制定，进度跟踪

* **2018.01 - 2018.09** 新零售无人称重货柜物联网项目(在线版) - 项目管理技术架构
  * **描述**：依托物联网技术实现智能化购物，无人化管理的新零售平台
  * **技术栈**：
    * Maven管理构建项目
    * 前端：thymeleaf、bootstrap、layer等框架
    * 后端：SpringBoot+Dubbo，MyBatis，Druid
    * 缓存与消息队列：Redis，RabbitMQ
    * 安全与部署：Shiro，Docker
  * **责任**：项目立项、技术架构、进度成本跟踪、测试交付全流程管理

### 数据集成与SOA项目

* **2015.01 - 2017.05** 南昌人社SOA数据集成交换平台 - 项目管理技术架构
  * **描述**：对南昌人社内现有业务系统进行数据及服务集成，形成"一网一平台"的基本业务功能框架
  * **技术**：金蝶ESB设计器，Eclipse，SoapUI，Linux，AESB，CXF，Oracle

* **2013.03 - 2014.05** 上海图书馆数据集成项目 - 项目管理技术架构
  * **描述**：解决上图馆现有项目之间信息孤岛问题，采用总线型架构设计
  * **技术**：金蝶ESB服务总线，实现各系统数据集成和服务编排

* **2012.08 - 2013.02** 上海浦东公交系统财务数据中心 - 项目管理技术架构
  * **描述**：打破原有财务系统各分部之间的数据孤立状态，实现统一网状架构
  * **技术**：金蝶ESB，金蝶MQ，SQL Server 2008

### 保险行业项目

* **2012.04 - 2012.07** 坐席运营系统 - 项目管理开发
  * **描述**：包括软电话呼叫中心，通用业务，产险和寿险业务受理、查询和回访等功能
  * **技术**：Spring，WebWork，Hibernate，WAS，WMG，Oracle

* **2011.08 - 2012.03** WebSphere ESB系统持续改造 - 项目管理开发
  * **描述**：基于产险ESB内部消息交互系统改造，包括程序重设计、监控系统升级等
  * **技术**：WebSphere Integration Developer，Eclipse，AIX，WebSphere ESB

* **2010.04 - 2011.11** 文件数据交互器 - 项目管理开发
  * **描述**：开发点对点文件传输适配器，弥补消息交换总线传输大文件的性能不足
  * **技术**：C语言，Socket编程，多线程机制，跨平台支持

* **2009.06 - 2010.01** 消息交换总线核心架构改造 - 项目管理开发
  * **描述**：以中心集群方式重新部署SOA中心系统，实现MB双工，MQ集群架构
  * **技术**：C语言，HP-UX

## 技能特长

* **编程语言**：精通Java，熟悉C/C++，Shell脚本
* **Web框架**：精通Spring全家桶(Spring MVC, Spring Boot, Spring Cloud)
* **中间件**：熟练使用IBM ESB, Message Broker, 金蝶ESB进行SOA开发
* **应用服务器**：熟练部署和优化WebLogic, WebSphere等
* **物联网**：熟悉物联网技术开发，精通Netty框架通信开发
* **移动开发**：熟悉微信公众号及小程序开发，了解Android开发
* **消息队列**：熟练使用IBM WMQ, ActiveMQ, RabbitMQ等
* **操作系统**：熟练使用Linux, AIX, HP-UX等
* **数据库**：精通Oracle, MySQL等关系型数据库
* **数据集成**：熟练使用Kettle, 金蝶ETL等数据同步工具
* **人工智能**：熟悉深度学习框架(TensorFlow, PyTorch)，自然语言处理技术
* **RAG技术**：熟悉Retrieval-Augmented Generation (RAG) 技术，能够结合知识库中的数据生成更准确和上下文相关的回答
* **开发工具**：熟练使用Cursor等开发工具，提高开发效率和代码质量
* **开源工具**：熟悉Dify等开源工具，优化系统性能和稳定性

## 自我评价

拥有超过15年的IT行业经验，其中包括10年以上的项目管理和团队管理经验。作为资深的SOA架构师、系统集成师和项目管理专家，我在**中国太平洋保险公司**的研发部从事基于IBM产品的消息总线项目长达7年，积累了丰富的企业级应用开发经验。

近年来，我专注于物联网和人工智能领域，熟悉创业型企业的运作模式和技术需求，能够快速适应不同行业的技术挑战，提供全面的解决方案。

我擅长技术团队的建设和管理，注重技术创新与业务价值的结合，能够高效推动项目从需求分析到交付的全流程实施。

**近年具体成就**：
- 成功领导多个大型项目，如上海杰盛立业公司内部数据私有知识库人工智能项目，显著提升了公司内部知识的智能化管理。
- 在上海科嘉智慧园区项目中，通过多种AI算法的集成，显著提升了园区的安全性和运营效率。
- 在企业CMMI3评选项目中，成功推动企业通过CMMI3认证，建立了完整的项目管理体系和标准流程。

**个人特质**：
- **领导力**：具备出色的领导力，能够带领团队高效完成项目目标。
- **创新思维**：善于创新，能够提出并实施新的技术解决方案。
- **沟通能力**：具备优秀的沟通能力，能够与不同背景的团队成员和客户有效沟通。
- **适应能力**：能够快速适应不同的工作环境和技术挑战。

**未来职业规划**：
- 继续深耕物联网和人工智能领域，探索更多技术创新的可能性。
- 希望在未来的项目中，能够带领团队实现更多的技术突破，为企业创造更大的价值。
- 通过不断学习和实践，提升自己的技术能力和管理能力，成为行业内的领军人物。