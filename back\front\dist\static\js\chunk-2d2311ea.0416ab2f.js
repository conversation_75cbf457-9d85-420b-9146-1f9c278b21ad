(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d2311ea"],{eed8:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.itemTitle,visible:e.itemOpen,fullscreen:"",width:"100%","append-to-body":""},on:{"update:visible":function(t){e.itemOpen=t},close:e.closeItem}},[a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"条码",prop:"code"}},[a("el-input",{attrs:{placeholder:"请输入条码",clearable:""},on:{clear:e.handleQuery},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.code,callback:function(t){e.$set(e.queryParams,"code",t)},expression:"queryParams.code"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},on:{clear:e.handleQuery,change:e.handleQuery},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.asset_fixed_inventory_item_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.fixedInventoryItemList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"盘点任务",align:"center",prop:"inventoryName"}}),a("el-table-column",{attrs:{label:"物资",align:"center",prop:"assetName"}}),a("el-table-column",{attrs:{label:"物资明细",align:"center",prop:"assetItemName"}}),a("el-table-column",{attrs:{label:"条码",align:"center",prop:"code"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.asset_fixed_inventory_item_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"盘点任务",prop:"inventoryId"}},[a("el-input",{attrs:{placeholder:"请输入盘点任务",maxlength:"50","show-word-limit":""},model:{value:e.form.inventoryId,callback:function(t){e.$set(e.form,"inventoryId",t)},expression:"form.inventoryId"}})],1),a("el-form-item",{attrs:{label:"物资明细",prop:"assetItemId"}},[a("el-input",{attrs:{placeholder:"请输入物资明细",maxlength:"50","show-word-limit":""},model:{value:e.form.assetItemId,callback:function(t){e.$set(e.form,"assetItemId",t)},expression:"form.assetItemId"}})],1),a("el-form-item",{attrs:{label:"物资",prop:"assetId"}},[a("el-input",{attrs:{placeholder:"请输入物资",maxlength:"50","show-word-limit":""},model:{value:e.form.assetId,callback:function(t){e.$set(e.form,"assetId",t)},expression:"form.assetId"}})],1),a("el-form-item",{attrs:{label:"条码",prop:"code"}},[a("el-input",{attrs:{placeholder:"请输入条码",maxlength:"50","show-word-limit":""},model:{value:e.form.code,callback:function(t){e.$set(e.form,"code",t)},expression:"form.code"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.asset_fixed_inventory_item_status,(function(t){return a("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label)+" ")])})),1)],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{placeholder:"请输入备注",maxlength:"50","show-word-limit":""},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)])},n=[],l=a("5530"),s=(a("d81d"),a("b0c0"),a("b775"));function o(e){return Object(s["a"])({url:"/asset/fixedInventoryItem/list",method:"get",params:e})}function i(e){return Object(s["a"])({url:"/asset/fixedInventoryItem/"+e,method:"get"})}function u(e){return Object(s["a"])({url:"/asset/fixedInventoryItem",method:"post",data:e})}function m(e){return Object(s["a"])({url:"/asset/fixedInventoryItem",method:"put",data:e})}function c(e){return Object(s["a"])({url:"/asset/fixedInventoryItem/"+e,method:"delete"})}var d={name:"FixedInventoryItem",dicts:["asset_fixed_inventory_item_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,fixedInventoryItemList:[],title:"",open:!1,itemOpen:!1,itemTitle:"盘点任务明细",queryParams:{pageNum:1,pageSize:10,inventoryId:null,assetItemId:null,assetId:null,code:null,status:null},form:{},rules:{inventoryId:[{required:!0,message:"盘点任务不能为空",trigger:"blur"}],assetItemId:[{required:!0,message:"物资明细不能为空",trigger:"blur"}],assetId:[{required:!0,message:"物资不能为空",trigger:"blur"}],code:[{required:!0,message:"条码不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.fixedInventoryItemList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,inventoryId:null,assetItemId:null,assetId:null,code:null,status:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加固定资产盘点明细"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;i(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改固定资产盘点明细"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?m(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除固定资产盘点明细编号为"'+a+'"的数据项？').then((function(){return c(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("asset/fixedInventoryItem/export",Object(l["a"])({},this.queryParams),"fixedInventoryItem_".concat((new Date).getTime(),".xlsx"))},openDialog:function(e){this.queryParams.inventoryId=e.id,this.itemTitle=e.name+"--盘点任务明细",this.itemOpen=!0,this.resetQuery()},closeItem:function(){this.itemOpen=!1}}},p=d,f=a("2877"),h=Object(f["a"])(p,r,n,!1,null,null,null);t["default"]=h.exports}}]);