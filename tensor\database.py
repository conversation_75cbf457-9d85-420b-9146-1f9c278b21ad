import mysql.connector
from datetime import datetime
from config import Config

class Database:
    def __init__(self):
        self.connection = mysql.connector.connect(
            host=Config.DB_HOST,
            database=Config.DB_NAME,
            user=Config.DB_USER,
            password=Config.DB_PASSWORD
        )
        self.cursor = self.connection.cursor()
        self._create_tables()

    def _create_tables(self):
        # 创建检测记录表
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS detection_records (
                id INT AUTO_INCREMENT PRIMARY KEY,
                timestamp DATETIME,
                garbage_type VARCHAR(50),
                confidence FLOAT,
                image_path VARCHAR(255),
                garbage_category VARCHAR(20)
            )
        ''')
        
        # 创建统计表
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS statistics (
                id INT AUTO_INCREMENT PRIMARY KEY,
                date DATE,
                garbage_type VARCHAR(50),
                count INT,
                category VARCHAR(20)
            )
        ''')
        self.connection.commit()

    def add_detection(self, garbage_type, confidence, image_path):
        category = Config.GARBAGE_TYPES.get(garbage_type, '未分类')
        sql = '''
            INSERT INTO detection_records 
            (timestamp, garbage_type, confidence, image_path, garbage_category)
            VALUES (%s, %s, %s, %s, %s)
        '''
        values = (datetime.now(), garbage_type, confidence, image_path, category)
        self.cursor.execute(sql, values)
        self.connection.commit()

    def update_statistics(self, garbage_type):
        category = Config.GARBAGE_TYPES.get(garbage_type, '未分类')
        today = datetime.now().date()
        
        # 检查是否存在今天的记录
        self.cursor.execute('''
            SELECT id, count FROM statistics 
            WHERE date = %s AND garbage_type = %s
        ''', (today, garbage_type))
        
        result = self.cursor.fetchone()
        if result:
            # 更新现有记录
            self.cursor.execute('''
                UPDATE statistics 
                SET count = count + 1 
                WHERE id = %s
            ''', (result[0],))
        else:
            # 创建新记录
            self.cursor.execute('''
                INSERT INTO statistics (date, garbage_type, count, category)
                VALUES (%s, %s, %s, %s)
            ''', (today, garbage_type, 1, category))
        
        self.connection.commit()

    def get_daily_statistics(self, date=None):
        if date is None:
            date = datetime.now().date()
        
        self.cursor.execute('''
            SELECT garbage_type, count, category 
            FROM statistics 
            WHERE date = %s
        ''', (date,))
        
        return self.cursor.fetchall()

    def close(self):
        self.cursor.close()
        self.connection.close() 