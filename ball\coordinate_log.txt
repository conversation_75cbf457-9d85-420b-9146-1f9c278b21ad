[<PERSON><PERSON> Dec 31 11:20:54 2024] 程序启动
[<PERSON><PERSON> Dec 31 11:20:54 2024] 对话框初始化
[<PERSON><PERSON> Dec 31 11:21:25 2024] 点击计算按钮
[<PERSON><PERSON> Dec 31 11:21:25 2024] 开始计算
[<PERSON><PERSON> Dec 31 11:21:25 2024] 开始验证输入
[<PERSON><PERSON> Dec 31 11:21:25 2024] 输入框 1001 的值: 123
[<PERSON><PERSON> Dec 31 11:21:25 2024] 输入框 1002 的值: 323
[<PERSON><PERSON> Dec 31 11:21:25 2024] 输入框 1003 的值: 332
[<PERSON><PERSON> Dec 31 11:21:25 2024] 输入框 1004 的值: 434
[<PERSON><PERSON> Dec 31 11:21:25 2024] 输入框 1005 的值: 434
[<PERSON><PERSON> Dec 31 11:21:25 2024] 输入框 1006 的值: 234
[Tu<PERSON> Dec 31 11:21:25 2024] 转换后的坐标值: (123.00,323.00), (332.00,434.00), (434.00,234.00)
[<PERSON><PERSON> Dec 31 11:21:25 2024] 计算结果: x=279.2388, y=281.0818
[<PERSON><PERSON> Dec 31 11:21:25 2024] 计算成功: x=279.2388, y=281.0818
[<PERSON><PERSON> Dec 31 11:21:25 2024] 计算完成
[Tu<PERSON> Dec 31 11:21:33 2024] 关闭对话框
[Tu<PERSON> Dec 31 11:21:33 2024] 程序退出
[Tue Dec 31 11:22:39 2024] 程序启动
[Tue Dec 31 11:22:39 2024] 对话框初始化
[Tue Dec 31 11:22:58 2024] 点击计算按钮
[Tue Dec 31 11:22:58 2024] 开始计算
[Tue Dec 31 11:22:58 2024] 开始验证输入
[Tue Dec 31 11:22:58 2024] 输入框 1001 的值: 121
[Tue Dec 31 11:22:58 2024] 输入框 1002 的值: 212
[Tue Dec 31 11:22:58 2024] 输入框 1003 的值: 121
[Tue Dec 31 11:22:58 2024] 输入框 1004 的值: 121
[Tue Dec 31 11:22:58 2024] 输入框 1005 的值: 213
[Tue Dec 31 11:22:58 2024] 输入框 1006 的值: 321
[Tue Dec 31 11:22:58 2024] 转换后的坐标值: (121.00,212.00), (121.00,121.00), (213.00,321.00)
[Tue Dec 31 11:22:58 2024] 计算结果: x=285.4783, y=166.5000
[Tue Dec 31 11:22:58 2024] 计算成功: x=285.4783, y=166.5000
[Tue Dec 31 11:22:58 2024] 计算完成
[Tue Dec 31 11:23:36 2024] 关闭对话框
[Tue Dec 31 11:23:36 2024] 程序退出
[Tue Dec 31 11:25:57 2024] 程序启动
[Tue Dec 31 11:25:57 2024] 对话框初始化
[Tue Dec 31 11:26:11 2024] 点击计算按钮
[Tue Dec 31 11:26:11 2024] 开始计算
[Tue Dec 31 11:26:11 2024] 开始验证输入
[Tue Dec 31 11:26:11 2024] 输入框 1001 的值: 343
[Tue Dec 31 11:26:11 2024] 输入框 1002 的值: 333
[Tue Dec 31 11:26:11 2024] 输入框 1003 的值: 323
[Tue Dec 31 11:26:11 2024] 输入框 1004 的值: 433
[Tue Dec 31 11:26:11 2024] 输入框 1005 的值: 432
[Tue Dec 31 11:26:11 2024] 输入框 1006 的值: 432
[Tue Dec 31 11:26:11 2024] 转换后的坐标值: (343.00,333.00), (323.00,433.00), (432.00,432.00)
[Tue Dec 31 11:26:11 2024] 计算结果: x=377.1268, y=391.8254
[Tue Dec 31 11:26:11 2024] 计算成功: x=377.1268, y=391.8254
[Tue Dec 31 11:26:11 2024] 计算完成
[Tue Dec 31 11:26:34 2024] 关闭对话框
[Tue Dec 31 11:26:34 2024] 程序退出
[Tue Dec 31 11:28:34 2024] 程序启动
[Tue Dec 31 11:28:34 2024] 对话框初始化
[Tue Dec 31 11:28:46 2024] 点击计算按钮
[Tue Dec 31 11:28:46 2024] 开始计算
[Tue Dec 31 11:28:46 2024] 开始验证输入
[Tue Dec 31 11:28:46 2024] 输入框 1001 的值: 3333
[Tue Dec 31 11:28:46 2024] 输入框 1002 的值: 4343
[Tue Dec 31 11:28:46 2024] 输入框 1003 的值: 4322
[Tue Dec 31 11:28:46 2024] 输入框 1004 的值: 4343
[Tue Dec 31 11:28:46 2024] 输入框 1005 的值: 4343
[Tue Dec 31 11:28:46 2024] 输入框 1006 的值: 4343
[Tue Dec 31 11:28:46 2024] 转换后的坐标值: (3333.00,4343.00), (4322.00,4343.00), (4343.00,4343.00)
[Tue Dec 31 11:28:46 2024] 检测到三点共线
[Tue Dec 31 11:28:47 2024] 计算失败
[Tue Dec 31 11:28:49 2024] 计算完成
[Tue Dec 31 11:29:00 2024] 关闭对话框
[Tue Dec 31 11:29:00 2024] 程序退出
[Tue Dec 31 11:29:47 2024] 程序启动
[Tue Dec 31 11:29:47 2024] 对话框初始化
[Tue Dec 31 11:29:52 2024] 关闭对话框
[Tue Dec 31 11:29:52 2024] 程序退出
[Tue Dec 31 11:30:10 2024] 程序启动
[Tue Dec 31 11:30:10 2024] 对话框初始化
[Tue Dec 31 11:30:24 2024] 点击计算按钮
[Tue Dec 31 11:30:24 2024] 开始计算
[Tue Dec 31 11:30:24 2024] 开始验证输入
[Tue Dec 31 11:30:24 2024] 输入框 1001 的值: 4343
[Tue Dec 31 11:30:24 2024] 输入框 1002 的值: 4343
[Tue Dec 31 11:30:24 2024] 输入框 1003 的值: 4343
[Tue Dec 31 11:30:24 2024] 输入框 1004 的值: 4343
[Tue Dec 31 11:30:24 2024] 输入框 1005 的值: 4343
[Tue Dec 31 11:30:24 2024] 输入框 1006 的值: 4343
[Tue Dec 31 11:30:24 2024] 转换后的坐标值: (4343.00,4343.00), (4343.00,4343.00), (4343.00,4343.00)
[Tue Dec 31 11:30:24 2024] 检测到重合点
[Tue Dec 31 11:30:25 2024] 计算失败
[Tue Dec 31 11:30:25 2024] 计算完成
[Tue Dec 31 11:30:26 2024] 点击重置按钮
[Tue Dec 31 11:30:26 2024] 数据已重置
[Tue Dec 31 11:31:38 2024] 关闭对话框
[Tue Dec 31 11:31:38 2024] 程序退出
[Tue Dec 31 11:34:11 2024] 程序启动
[Tue Dec 31 11:34:11 2024] 对话框初始化
[Tue Dec 31 11:34:37 2024] 点击计算按钮
[Tue Dec 31 11:34:37 2024] 开始计算
[Tue Dec 31 11:34:37 2024] 开始验证输入
[Tue Dec 31 11:34:37 2024] 输入框 1001 的值: 1111
[Tue Dec 31 11:34:37 2024] 输入框 1002 的值: 2222
[Tue Dec 31 11:34:37 2024] 输入框 1003 的值: 2222
[Tue Dec 31 11:34:37 2024] 输入框 1004 的值: 1111
[Tue Dec 31 11:34:37 2024] 输入框 1005 的值: 3333
[Tue Dec 31 11:34:37 2024] 输入框 1006 的值: 4444
[Tue Dec 31 11:34:37 2024] 转换后的坐标值: (1111.00,2222.00), (2222.00,1111.00), (3333.00,4444.00)
[Tue Dec 31 11:34:37 2024] 计算结果: x=2777.5000, y=2777.5000
[Tue Dec 31 11:34:37 2024] 计算成功: x=2777.5000, y=2777.5000
[Tue Dec 31 11:34:37 2024] 计算完成
[Tue Dec 31 11:35:46 2024] 关闭对话框
[Tue Dec 31 11:35:47 2024] 程序退出
[Tue Dec 31 11:35:52 2024] 程序启动
[Tue Dec 31 11:35:52 2024] 对话框初始化
[Tue Dec 31 11:35:59 2024] 关闭对话框
[Tue Dec 31 11:35:59 2024] 程序退出
[Tue Dec 31 11:36:00 2024] 程序启动
[Tue Dec 31 11:36:00 2024] 对话框初始化
[Tue Dec 31 11:36:13 2024] 点击计算按钮
[Tue Dec 31 11:36:13 2024] 开始计算
[Tue Dec 31 11:36:13 2024] 开始验证输入
[Tue Dec 31 11:36:13 2024] 输入框 1001 的值: 2222
[Tue Dec 31 11:36:13 2024] 输入框 1002 的值: 3333
[Tue Dec 31 11:36:13 2024] 输入框 1003 的值: 2222
[Tue Dec 31 11:36:13 2024] 输入框 1004 的值: 3333
[Tue Dec 31 11:36:13 2024] 输入框 1005 的值: 4343
[Tue Dec 31 11:36:13 2024] 输入框 1006 的值: 4322
[Tue Dec 31 11:36:13 2024] 转换后的坐标值: (2222.00,3333.00), (2222.00,3333.00), (4343.00,4322.00)
[Tue Dec 31 11:36:13 2024] 检测到重合点
[Tue Dec 31 11:36:15 2024] 计算失败
[Tue Dec 31 11:36:17 2024] 计算完成
[Tue Dec 31 11:36:25 2024] 点击计算按钮
[Tue Dec 31 11:36:25 2024] 开始计算
[Tue Dec 31 11:36:25 2024] 开始验证输入
[Tue Dec 31 11:36:25 2024] 输入框 1001 的值: 2222
[Tue Dec 31 11:36:25 2024] 输入框 1002 的值: 3333
[Tue Dec 31 11:36:25 2024] 输入框 1003 的值: 2222
[Tue Dec 31 11:36:25 2024] 输入框 1004 的值: 2233
[Tue Dec 31 11:36:25 2024] 输入框 1005 的值: 4343
[Tue Dec 31 11:36:25 2024] 输入框 1006 的值: 4322
[Tue Dec 31 11:36:25 2024] 转换后的坐标值: (2222.00,3333.00), (2222.00,2233.00), (4343.00,4322.00)
[Tue Dec 31 11:36:25 2024] 计算结果: x=3769.5394, y=2783.0000
[Tue Dec 31 11:36:25 2024] 计算成功: x=3769.5394, y=2783.0000
[Tue Dec 31 11:36:25 2024] 计算完成
[Tue Dec 31 11:36:54 2024] 关闭对话框
[Tue Dec 31 11:36:54 2024] 程序退出
[Tue Dec 31 11:38:49 2024] 程序启动
[Tue Dec 31 11:38:49 2024] 对话框初始化
[Tue Dec 31 11:41:38 2024] 关闭对话框
[Tue Dec 31 11:41:38 2024] 程序退出
