<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>输入提示后查询</title>
    <link rel="stylesheet" href="https://cache.amap.com/lbs/static/main1119.css"/>
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.5&key=a16113d4de4bb31c0b50509fe949f789&plugin=AMap.PolygonEdito,AMap.Autocomplete,AMap.PlaceSearch"></script>
    <script type="text/javascript" src="https://cache.amap.com/lbs/static/addToolbar.js"></script>
</head>
<body>
<div id="container"></div>
<div id="myPageTop">
    <table>
        <tr>
            <td>
                <label>请输入关键字：</label>
            </td>
        </tr>
        <tr>
            <td>
                <input id="tipinput"/>
            </td>
        </tr>
        <tr>
        	<td>
        		<button onclick="removeMarkers()">清除</button>
				<button onclick="getLine()">获取连线</button>
			<td/>
        </tr>
        <tr>
            <td>
                <label>请输入导出数据文件名：</label>
            </td>
        </tr>
       	<tr>
       		<td>
       			<input id="name"/>
       		<td/>
       	</tr>
       	<tr>
       		<td>
       			<button onclick="saveFile()">导出数据</button>
       		</td>
       	</tr>
    </table>
    
	
</div>

<script type="text/javascript">
    //地图加载
    var map = new AMap.Map("container", {
        resizeEnable: true
    });
    var markers = [];
    var points = []
    //输入提示
    var autoOptions = {
        input: "tipinput"
    };
    map.on('click',getPoints);
    var auto = new AMap.Autocomplete(autoOptions);
    var placeSearch = new AMap.PlaceSearch({
        map: map
    });  //构造地点查询类
    AMap.event.addListener(auto, "select", select);//注册监听，当选中某条记录时会触发
    function select(e) {
    	if(e.poi && e.poi.location){
    		map.setZoom(20);
    		map.setCenter(e.poi.location);
    	}
        //placeSearch.setCity(e.poi.adcode);
        //placeSearch.search(e.poi.name);  //关键字查询查询
    }
    function removeMarkers(){
    	//console.log(1);
    	markers = [];
    	points = [];
    	document.getElementById('name').value = '';
        map.clearMap();
    }
    function getPoints(e){
    	var lng = e.lnglat.getLng();
    	var lat = e.lnglat.getLat();
    	//points.push([lng,lat]);
    	var marker = new AMap.Marker({
	        position: new AMap.LngLat(lng,lat),
	        lng:lng,
	        lat:lat
	    });
	    markers.push(marker);
	    points.push([lng,lat]);
    	map.add(marker);
    }
    function getLine(){
    	var color = '#0055ff';
    	if(markers.length>1){
    		// for(var i=0;i<markers.length;i++){
    			
    		// }
    		var line = new AMap.Polygon({
                path: points,
                strokeColor: "#71B3ff", //#3d5b78
                strokeWeight: 1,
                strokeOpacity: 1,
                fillOpacity: 0.4,
                //fillColor: 'yellow', //#5b77bf
                zIndex: 50,
                strokeStyle: 'dashed',//虚线
            });
            map.add(line);
    	}
    	else{
    		alert('请选择至少两个坐标！');
    		return;
    	}
    }
    function saveFile(){
    	var name = document.getElementById('name').value;
    	if(name!=''&&name!=null&&name!=undefined){
	    	if(points.length>0){
	    		var data = points;
		  		// var data = '自定义的数据内容，可以是服务端返回滴！';
		  		var names = name + '.txt';
		  		exportRaw(data, names);
		  		removeMarkers();
	    	}
		  	else{
	    		alert('请选择至少两个坐标！');
	    		return;
	    	}
    	}
    	else{
    		alert('请填写导出数据名称！');
	    	return;
    	}
	}
	function exportRaw(data, name) {
	  var urlObject = window.URL || window.webkitURL || window;
	  var export_blob = new Blob([data]);
	  var save_link = document.createElementNS("http://www.w3.org/1999/xhtml", "a")
	  save_link.href = urlObject.createObjectURL(export_blob);
	  save_link.download = name;
	  save_link.click();
	}
</script>
</body>
</html>