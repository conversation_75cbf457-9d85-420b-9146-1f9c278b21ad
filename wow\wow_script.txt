/script A =GetActionCooldown;
/script U =IsUsableAction;
/script C =CastSpellByName;
/script ii=90;
/script combat =UnitAffectingCombat("player");
/script s=nil;
/script if not combat then _,_,s=GetShapeshiftFormInfo(1);end;
/script if not s and not combat then C("潜行"); end;
/script local mhs= GetInventoryItemLink("player",16); if s and mhs and not strfind( mhs ,"锋刃匕首 力量之") then PickupContainerItem(0,1);PickupInventoryItem(16); end;
/script _,_,s=GetShapeshiftFormInfo(1);
/script if s then C("伏击");end;
/script local mm= GetInventoryItemLink("player",16);if not s and mm and not strfind( mm ,"耀光之锤") then PickupContainerItem(0,1);PickupInventoryItem(16); end;
/script p=GetComboPoints(); h =UnitHealth("target");
/script hh =UnitHealth("target")/UnitHealthMax("target");
/script f=nil;w=nil;k=nil;
/script local i,b for i=1,8 do b=UnitBuff("player",i);if b and strfind(b,"SliceDice") then f=1;end; if b and strfind(b,"DualWield") then w=1; end; if b and strfind(b,"毒伤") then k=1; end; end;
/script if A(4) then C("鬼魅攻击"); end;
/script if p>0 and h<ii*1 then C("剔骨");end;
/script if p>1 and h<ii*2 then C("剔骨");end;
/script if p>2 and h<ii*3 then C("剔骨");end;
/script if p>3 and h<ii*4 then C("剔骨");end;
/script if p>3 and f and A(7) then C("冷血");C("剔骨");end;
/script if p>3 and f then C("剔骨");end;
/script if p>0 and A(3) and not k then C("毒伤"); end;
/script if p>0 and not f  then C("切割");end;
/script if not s then C("邪恶攻击");end;