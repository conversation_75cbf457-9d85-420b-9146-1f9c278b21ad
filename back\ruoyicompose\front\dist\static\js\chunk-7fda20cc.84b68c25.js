(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7fda20cc","chunk-2d0c22ce"],{"48d8":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.itemTitle,visible:e.itemOpen,fullscreen:"",width:"100%","append-to-body":""},on:{"update:visible":function(t){e.itemOpen=t},close:e.closeItem}},[a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"条码",prop:"epc"}},[a("el-input",{attrs:{placeholder:"请输入条码",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.epc,callback:function(t){e.$set(e.queryParams,"epc",t)},expression:"queryParams.epc"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.asset_inventory_item_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"danger",plain:"",icon:"el-icon-close",size:"mini"},on:{click:e.closeItem}},[e._v("关闭 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:inventoryItem:export"],expression:"['asset:inventoryItem:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.inventoryItemList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"盘点任务",align:"center",prop:"inventoryName"}}),a("el-table-column",{attrs:{label:"物资",align:"center",prop:"assetName"}}),a("el-table-column",{attrs:{label:"条码",align:"center",prop:"epc"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.asset_inventory_item_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"物资明细",prop:"assetItemId"}},[a("el-input",{attrs:{placeholder:"请输入物资明细",maxlength:"50","show-word-limit":""},model:{value:e.form.assetItemId,callback:function(t){e.$set(e.form,"assetItemId",t)},expression:"form.assetItemId"}})],1),a("el-form-item",{attrs:{label:"物资",prop:"assetId"}},[a("el-input",{attrs:{placeholder:"请输入物资",maxlength:"50","show-word-limit":""},model:{value:e.form.assetId,callback:function(t){e.$set(e.form,"assetId",t)},expression:"form.assetId"}})],1),a("el-form-item",{attrs:{label:"条码",prop:"epc"}},[a("el-input",{attrs:{placeholder:"请输入条码",maxlength:"50","show-word-limit":""},model:{value:e.form.epc,callback:function(t){e.$set(e.form,"epc",t)},expression:"form.epc"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.asset_inventory_item_status,(function(t){return a("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label)+" ")])})),1)],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{placeholder:"请输入备注",maxlength:"50","show-word-limit":""},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)])},r=[],s=a("5530"),i=(a("d81d"),a("b0c0"),a("b775"));function l(e){return Object(i["a"])({url:"/asset/inventoryItem/list",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/asset/inventoryItem/"+e,method:"get"})}function u(e){return Object(i["a"])({url:"/asset/inventoryItem",method:"post",data:e})}function c(e){return Object(i["a"])({url:"/asset/inventoryItem",method:"put",data:e})}function m(e){return Object(i["a"])({url:"/asset/inventoryItem/"+e,method:"delete"})}var d={name:"InventoryItem",dicts:["asset_inventory_item_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,inventoryItemList:[],title:"",open:!1,itemOpen:!1,itemTitle:"盘点任务明细",queryParams:{pageNum:1,pageSize:10,inventoryId:null,assetItemId:null,assetId:null,epc:null,status:null},form:{},rules:{inventoryId:[{required:!0,message:"盘点任务不能为空",trigger:"blur"}],assetItemId:[{required:!0,message:"物资明细不能为空",trigger:"blur"}],assetId:[{required:!0,message:"物资不能为空",trigger:"blur"}],epc:[{required:!0,message:"条码不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}},methods:{getList:function(){var e=this;this.loading=!0,l(this.queryParams).then((function(t){e.inventoryItemList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,inventoryId:null,assetItemId:null,assetId:null,epc:null,status:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加盘点明细"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;o(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改盘点明细"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?c(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除盘点明细编号为"'+a+'"的数据项？').then((function(){return m(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("asset/inventoryItem/export",Object(s["a"])({},this.queryParams),"inventoryItem_".concat((new Date).getTime(),".xlsx"))},openDialog:function(e){this.itemOpen=!0,this.queryParams.inventoryId=e.id,this.itemTitle=e.deptName+"--"+e.name+"--盘点任务明细",this.resetQuery()},closeItem:function(){this.itemOpen=!1}}},p=d,h=a("2877"),f=Object(h["a"])(p,n,r,!1,null,null,null);t["default"]=f.exports},"6b08":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"区库",prop:"deptId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择区库",clearable:"",filterable:""},on:{change:e.handleQuery,clear:e.handleQuery},model:{value:e.queryParams.deptId,callback:function(t){e.$set(e.queryParams,"deptId",t)},expression:"queryParams.deptId"}},e._l(e.deptOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.text,value:parseInt(e.value)}})})),1)],1),a("el-form-item",{attrs:{label:"任务名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入任务名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},on:{change:e.handleQuery,clear:e.handleQuery},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.asset_inventory_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:inventory:add"],expression:"['asset:inventory:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:inventory:export"],expression:"['asset:inventory:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.inventoryList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"区库",align:"center",prop:"deptName"}}),a("el-table-column",{attrs:{label:"任务名称",align:"center",prop:"name"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.asset_inventory_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:inventory:edit"],expression:"['asset:inventory:edit']"}],staticStyle:{color:"#55ed27"},attrs:{size:"mini",type:"text",icon:"el-icon-help"},on:{click:function(a){return e.handleItem(t.row)}}},[e._v("明细 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:inventory:edit"],expression:"['asset:inventory:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["asset:inventory:remove"],expression:"['asset:inventory:remove']"}],staticStyle:{color:"#ff0000"},attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"区库",prop:"deptId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择区库",clearable:"",filterable:""},model:{value:e.form.deptId,callback:function(t){e.$set(e.form,"deptId",t)},expression:"form.deptId"}},e._l(e.deptOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.text,value:parseInt(e.value)}})})),1)],1),a("el-form-item",{attrs:{label:"任务名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入任务名称",maxlength:"50","show-word-limit":""},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{placeholder:"请输入备注",maxlength:"50","show-word-limit":""},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("inventory-item",{ref:"inventoryItemRef"})],1)},r=[],s=a("5530"),i=(a("d81d"),a("b775"));function l(e){return Object(i["a"])({url:"/asset/inventory/list",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/asset/inventory/"+e,method:"get"})}function u(e){return Object(i["a"])({url:"/asset/inventory",method:"post",data:e})}function c(e){return Object(i["a"])({url:"/asset/inventory",method:"put",data:e})}function m(e){return Object(i["a"])({url:"/asset/inventory/"+e,method:"delete"})}var d=a("48d8"),p=a("fcb7"),h={name:"Inventory",components:{InventoryItem:d["default"]},dicts:["asset_inventory_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,inventoryList:[],deptOptions:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,deptId:null,name:null,status:null},form:{},rules:{deptId:[{required:!0,message:"区库不能为空",trigger:"change"}],name:[{required:!0,message:"任务名称不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,l(this.queryParams).then((function(t){e.inventoryList=t.rows,e.total=t.total,e.loading=!1}))},getDeptOption:function(){var e=this;Object(p["f"])({type:"1"}).then((function(t){e.deptOptions=t.data}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,deptId:null,name:null,status:"0",createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form"),this.getDeptOption()},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加资产盘点",this.getDeptOption()},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;o(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改资产盘点"})),this.getDeptOption()},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?c(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleItem:function(e){this.$refs["inventoryItemRef"].openDialog(e)},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除资产盘点编号为"'+a+'"的数据项？').then((function(){return m(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("asset/inventory/export",Object(s["a"])({},this.queryParams),"inventory_".concat((new Date).getTime(),".xlsx"))}}},f=h,y=a("2877"),v=Object(y["a"])(f,n,r,!1,null,null,null);t["default"]=v.exports},fcb7:function(e,t,a){"use strict";a.d(t,"f",(function(){return r})),a.d(t,"d",(function(){return s})),a.d(t,"e",(function(){return i})),a.d(t,"c",(function(){return l})),a.d(t,"a",(function(){return o})),a.d(t,"g",(function(){return u})),a.d(t,"b",(function(){return c}));var n=a("b775");function r(e){return Object(n["a"])({url:"/system/dept/option",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/system/dept/list",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/system/dept/list/exclude/"+e,method:"get"})}function l(e){return Object(n["a"])({url:"/system/dept/"+e,method:"get"})}function o(e){return Object(n["a"])({url:"/system/dept",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/system/dept",method:"put",data:e})}function c(e){return Object(n["a"])({url:"/system/dept/"+e,method:"delete"})}}}]);