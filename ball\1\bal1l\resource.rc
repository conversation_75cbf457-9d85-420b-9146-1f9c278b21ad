#pragma code_page(65001)
#include <windows.h>
#include "resource.h"

IDD_MAIN_DIALOG DIALOG DISCARDABLE 0, 0, 300, 220
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "抓躺尸"
FONT 9, "宋体"
BEGIN
    // 标题和说明
    LTEXT           "请输入三个躺尸消失点坐标：", -1, 20, 10, 150, 8
    LTEXT           "作者：凉巢萎", -1, 200, 10, 80, 8

    // 第一个点
    GROUPBOX        "第一个消失点", -1, 20, 25, 250, 40
    LTEXT           "经度:", -1, 30, 45, 25, 8
    EDITTEXT        IDC_EDIT1, 60, 43, 40, 14, ES_AUTOHSCROLL | ES_NUMBER | ES_RIGHT
    LTEXT           "纬度:", -1, 150, 45, 25, 8
    EDITTEXT        IDC_EDIT2, 180, 43, 40, 14, ES_AUTOHSCROLL | ES_NUMBER | ES_RIGHT

    // 第二个点
    GROUPBOX        "第二个消失点", -1, 20, 70, 250, 40
    LTEXT           "经度:", -1, 30, 90, 25, 8
    EDITTEXT        IDC_EDIT3, 60, 88, 40, 14, ES_AUTOHSCROLL | ES_NUMBER | ES_RIGHT
    LTEXT           "纬度:", -1, 150, 90, 25, 8
    EDITTEXT        IDC_EDIT4, 180, 88, 40, 14, ES_AUTOHSCROLL | ES_NUMBER | ES_RIGHT

    // 第三个点
    GROUPBOX        "第三个消失点", -1, 20, 115, 250, 40
    LTEXT           "经度:", -1, 30, 135, 25, 8
    EDITTEXT        IDC_EDIT5, 60, 133, 40, 14, ES_AUTOHSCROLL | ES_NUMBER | ES_RIGHT
    LTEXT           "纬度:", -1, 150, 135, 25, 8
    EDITTEXT        IDC_EDIT6, 180, 133, 40, 14, ES_AUTOHSCROLL | ES_NUMBER | ES_RIGHT

    // 按钮和结果显示区域
    PUSHBUTTON      "躺尸点", IDC_CALC_BUTTON, 20, 165, 50, 14
    PUSHBUTTON      "重置", IDC_RESET_BUTTON, 20, 185, 50, 14
    GROUPBOX        "躺尸点坐标", -1, 80, 160, 190, 45
    LTEXT           "", IDC_STATIC_RESULT1, 90, 175, 90, 8
    LTEXT           "", IDC_STATIC_RESULT2, 180, 175, 90, 8
END