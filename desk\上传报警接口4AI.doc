<html xmlns:v="urn:schemas-microsoft-com:vml"
    xmlns:o="urn:schemas-microsoft-com:office:office"
    xmlns:w="urn:schemas-microsoft-com:office:word"
    xmlns="http://www.w3.org/TR/REC-html40">
    <head><meta http-equiv=Content-Type content="text/html; charset=utf-8">
    <style type="text/css">
        table  
        {  
            border-collapse: collapse;
            border: none;  
            width: 100%;  
        }  
        td,tr  
        {  
            border: solid #CCC 1px;
            padding:3px;
            font-size:9pt;
        } 
        .codestyle{
            word-break: break-all;
            mso-highlight:rgb(252, 252, 252);
            padding-left: 5px; background-color: rgb(252, 252, 252); border: 1px solid rgb(225, 225, 232);
        }
        img {
            width:100;
        }
    </style>
    <meta name=ProgId content=Word.Document>
    <meta name=Generator content="Microsoft Word 11">
    <meta name=Originator content="Microsoft Word 11">
    <xml><w:WordDocument><w:View>Print</w:View></xml></head>
    <body><h1>上传报警接口</h1><div style="margin-left:20px;"><h5>简要描述</h5>
<ul>
<li>无</li>
</ul>
<h5>请求URL</h5>
<ul>
<li><code>https://jesonliye.com/system/workorderInfoTem/add</code> </li>
</ul>
<h5>请求方式</h5>
<ul>
<li>post</li>
</ul>
<h5>请求Body参数</h5>
<table>
<thead><tr style='background-color: rgb(0, 136, 204); color: rgb(255, 255, 255);'>
<th style="text-align: left;">参数名</th>
<th style="text-align: left;">必选</th>
<th>类型</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: left;">eqid</td>
<td style="text-align: left;">是</td>
<td>string</td>
<td>设备ID</td>
</tr>
<tr>
<td style="text-align: left;">workorderPicture</td>
<td style="text-align: left;">是</td>
<td>string</td>
<td>抓拍第一张</td>
</tr>
<tr>
<td style="text-align: left;">workorderPictureTwo</td>
<td style="text-align: left;">是</td>
<td>string</td>
<td>抓拍第二张</td>
</tr>
<tr>
<td style="text-align: left;">equipmentName</td>
<td style="text-align: left;">是</td>
<td>string</td>
<td>设备名称（厢房名称）</td>
</tr>
<tr>
<td style="text-align: left;">alarmType</td>
<td style="text-align: left;">是</td>
<td>string</td>
<td>报警类型（1实时检测垃圾异常、2间隔检测垃圾异常、3违规摆摊异常）</td>
</tr>
<tr>
<td style="text-align: left;">workorderPictureFour</td>
<td style="text-align: left;">是</td>
<td>string</td>
<td>抓拍第四张</td>
</tr>
<tr>
<td style="text-align: left;">workorderPictureFive</td>
<td style="text-align: left;">是</td>
<td>string</td>
<td>抓拍第五张</td>
</tr>
<tr>
<td style="text-align: left;">workorderPictureThree</td>
<td style="text-align: left;">是</td>
<td>string</td>
<td>抓拍第三张</td>
</tr>
<tr>
<td style="text-align: left;">workorderRemark</td>
<td style="text-align: left;">是</td>
<td>string</td>
<td>垃圾类型</td>
</tr>
</tbody>
</table>
<h5>成功返回示例</h5>
<table width='100%' class='codestyle'><pre><code>{
    "msg": "操作成功",
    "code": 0
}
</code></pre></table>
<h5>成功返回示例的参数说明</h5>
<table>
<thead><tr style='background-color: rgb(0, 136, 204); color: rgb(255, 255, 255);'>
<th style="text-align: left;">参数名</th>
<th style="text-align: left;">类型</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: left;">msg</td>
<td style="text-align: left;">string</td>
<td>操作成功与否信息</td>
</tr>
<tr>
<td style="text-align: left;">code</td>
<td style="text-align: left;">string</td>
<td>返回标识，0成功，1失败</td>
</tr>
</tbody>
</table></div></body></html>