测试局域网中 PUT 和 DELETE 请求是否被网关屏蔽

环境要求：
- 服务端：CentOS 7
- 客户端：Windows 11

1. CentOS 7 服务器端配置：

# 安装 Nginx（如果没有安装）
yum install -y nginx

# 创建测试配置文件
cat << 'EOF' > /etc/nginx/conf.d/test.conf
server {
    listen 8000;
    
    location /test {
        add_header Content-Type application/json;
        
        if ($request_method = PUT) {
            return 200 '{"method":"PUT","status":"ok"}';
        }
        
        if ($request_method = DELETE) {
            return 200 '{"method":"DELETE","status":"ok"}';
        }
        
        if ($request_method = GET) {
            return 200 '{"method":"GET","status":"ok"}';
        }
    }
}
EOF

# 启动 Nginx
systemctl start nginx

# 开放防火墙端口
firewall-cmd --zone=public --add-port=8000/tcp --permanent
firewall-cmd --reload

2. Windows 11 客户端测试方法：

方法一：使用 PowerShell 命令测试

# 测试 GET 请求
Invoke-WebRequest -Method GET -Uri "http://目标IP:8000/test" -UseBasicParsing

# 测试 PUT 请求
Invoke-WebRequest -Method PUT -Uri "http://目标IP:8000/test" -UseBasicParsing

# 测试 DELETE 请求
Invoke-WebRequest -Method DELETE -Uri "http://目标IP:8000/test" -UseBasicParsing

方法二：使用 Telnet 测试

1) 启用 Telnet 客户端：
   - 打开控制面板
   - 程序和功能
   - 启用或关闭 Windows 功能
   - 勾选 "Telnet 客户端"
   - 点击确定

2) 使用 Telnet 测试：
   telnet 目标IP 8000
   
   在连接后输入（按两次回车）：
   PUT /test HTTP/1.1
   Host: localhost

3. 在 CentOS 7 上查看请求日志：

tail -f /var/log/nginx/access.log

4. 结果分析：

成功情况：
- 状态码 200：表示该方法没有被屏蔽
- 网络连接正常

失败情况：
- 连接超时：可能被网关屏蔽
- 403/401：权限问题
- 405：方法被禁止

5. 故障排除：

如果无法连接：
- 检查防火墙设置
- 确认端口是否开放
- 验证 IP 地址是否正确

如果返回错误：
- 检查 Nginx 错误日志：tail -f /var/log/nginx/error.log
- 确认 Nginx 配置是否正确
- 验证服务是否正常运行：systemctl status nginx

注意事项：
1. 将命令中的"目标IP"替换为实际的 CentOS 7 服务器 IP 地址
2. 确保两台机器在同一个局域网内
3. 测试前先用 ping 命令确认基本连通性
4. 如果使用了防火墙，确保相关端口已开放 